export const RESOURCES = {
  PURCHASE_ORDER: 'purchase_order',
  HARVEST: 'harvest',
  TRIP: 'trip',
  SHIPMENT: 'shipment',
  TRANSFER_ORDER: 'transfer_orders',
  SALE_ORDER: 'sale_order',
  DELIVERY: 'delivery',
};

export const ACTIONS = {
  READ: 'read',
  CREATE: 'create',
  UPDATE: 'update',
  DELETE: 'delete',
};

export const LOT_TYPE = {
  //! Weight lot
  NON_STANDARD: 1,
  //! Box lot
  STANDARD: 2,
};

export const DC = {
  DIRECT_CUSTOMER: 6,
};

export const STANDARD_PACKAGING_WEIGHT = 2;

export const INWARD_TYPE = {
  TRUCK: 'TRUCK',
  CRATES: 'CRATES',
};

export const PACKAGING_ITEM = {
  LOOSE: 'Loose',
  HD_CRATE_OLD: 'HD Crate_Old',
  BOX_WHITE_10KG_5PLY_SQUARE: 'Box-White-10kg-5Ply(Square)',
  BIN: 'Bin',
};

export const INWARD_TYPE_PREFIX = {
  TRUCK: 'T',
  CRATES: 'C',
};

export const INWARD_TYPE_OPTIONS = [
  { text: INWARD_TYPE.CRATES, value: INWARD_TYPE_PREFIX.CRATES },
  { text: INWARD_TYPE.TRUCK, value: INWARD_TYPE_PREFIX.TRUCK },
];

export const CUSTOMERS = {
  VEGROW: {
    customer_id: -1,
    id: -1,
    name: 'Vegrow',
    customer_short_code: 'AT(JM)',
  },
  RETURN_TO_FARMER: {
    customer_id: -2,
    id: -2,
    name: 'Return to farmer',
    customer_short_code: 'RTF',
  },
  FRUIT_X: {
    customer_id: -3,
    id: -3,
    name: 'FruitX',
    customer_short_code: 'FX',
  },
  NEW_CUSTOMER: {
    customer_id: -4,
    id: -4,
    name: 'New Customer',
    customer_short_code: 'NEW',
  },
};

export const CUSTOMER_NAMES = {
  '-1': {
    name: 'Vegrow',
    customer_short_code: 'AT(JM)',
  },
  '-2': {
    name: 'Return to farmer',
    customer_short_code: 'RTF',
  },
  '-3': {
    name: 'FruitX',
    customer_short_code: 'FX',
  },
  '-4': {
    name: 'New Customer',
    customer_short_code: 'NEW',
  },
};

export const getCustomersName = data => {
  if (data?.customer_id >= 0) {
    return {
      name: data?.customer_name || '',
      customer_short_code: data?.customer_short_code || '',
    };
  } else if (data?.customer_id === -5) {
    return {
      name: data?.dcl_breached_customer_name || '',
      customer_short_code: data?.dcl_breached_customer_short_code || '',
    };
  }

  return {
    name: CUSTOMER_NAMES[data?.customer_id]?.name || '',
    customer_short_code:
      CUSTOMER_NAMES[data?.customer_id]?.customer_short_code || '',
  };
};

export const ADMIN_CONFIG_UNIT = {
  KG: 'KG',
};

export const CANCEL_REASON = [
  {
    id: 1,
    name: 'Change Inward type - requested by Farmer',
  },
  {
    id: 2,
    name: 'Farmer is taking back the produce',
  },
  { id: 3, name: 'Incorrect date selected' },
  { id: 5, name: 'Wrong vendor selected' },
  { id: 4, name: 'Others' },
];

export const PAGE_SIZE = 25;
export const PAGE_SIZE_SMALL = 10;

export const ROLES = {
  GATE_IN: 'gate_in',
};

export const TRANSACTION_TYPE = {
  MANDI_FL_PAY: 'MANDI_FL_PAY',
  MANDI_FL: 'MANDI_FL',
};

export const DEVICE_TYPE = {
  TABLET: 'Tablet',
  MOBILE: 'Mobile',
  DESKTOP: 'Desktop',
};
