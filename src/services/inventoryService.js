import { mandiService } from './base';

/**
 * Fetch inventory data based on filter parameters
 * @param {Object} params - Query parameters
 * @param {string} params.token - Optional token filter
 * @param {string} params.farmer_id - Optional farmer ID filter
 * @param {string} params.customer_id - Optional customer ID filter
 * @param {string} params.mandi_number_id - Optional mandi number ID filter
 * @param {string} params.loading_status - Optional status filter (YET_TO_LOAD, PARTIALLY_LOADED, FULLY_LOADED)
 * @returns {Promise} - API response
 */
export const fetchInventoryData = async (params = {}) => {
  try {
    const response = await mandiService.get('/inventory', {
      params,
    });

    if (response.responseData && response.responseData.mandi_numbers) {
      return {
        items: response.responseData.mandi_numbers,
        total: response.responseData.total || 0
      };
    }
    return { items: [], total: 0 };
  } catch (error) {
    console.error('Error fetching inventory data:', error);
    throw error;
  }
};

/**
 * Dispatch inventory with transportation details
 * @param {Object} data - The dispatch data
 * @param {string} data.driver_name - Name of the driver
 * @param {string} data.driver_contact - Contact number of the driver
 * @param {string} data.vehicle_number - Vehicle number for transportation
 * @param {string} data.transaction_date - ISO format date-time of the dispatch
 * @param {number} data.mandi_number_id - Mandi number ID
 * @param {Array} data.lots - Array of lot objects to dispatch
 * @param {number} data.lots[].lot_id - Lot ID
 * @param {number} data.lots[].dispatched_units - Number of units to dispatch
 * @returns {Promise} - API response
 */
export const dispatchInventory = async (data) => {
  try {
    const response = await mandiService.post('/inventory/transactions', data);
    console.log('Dispatch response:', response.data);
    return response.data;
  } catch (error) {
    console.error('Error dispatching inventory:', error);
    throw error;
  }
};

/**
 * Fetch inventory transactions for a specific mandi number ID
 * @param {number} mandiNumberId - The mandi number ID to fetch transactions for
 * @returns {Promise} - API response with transaction data
 */
export const fetchInventoryTransactions = async (mandiNumberId) => {
  try {
    if (!mandiNumberId) {
      throw new Error('Mandi number ID is required');
    }
    
    const response = await mandiService.get('/inventory/transactions', {
      params: { mandi_number_id: mandiNumberId }
    });
    
    if (response.responseData) {
      return response.responseData;
    }
    return [];
  } catch (error) {
    console.error(`Error fetching transactions for mandi number ID ${mandiNumberId}:`, error);
    throw error;
  }
};

/**
 * Fetch delivery challan data
 * @param {Object} params - Query parameters
 * @param {string} params.transaction_date - Transaction date (YYYY-MM-DD)
 * @param {string} params.customer_id - Customer ID
 * @param {string} params.vehicle_number - Vehicle number
 * @returns {Promise} - API response with delivery challan data
 */
export const fetchDeliveryChallan = async (params = {}) => {
  try {
    const response = await mandiService.get('/inventory/dispatchchallan', {
      params
    });
    
    if (response.responseData) {
      return response.responseData;
    }
    return null;
  } catch (error) {
    console.error('Error fetching delivery challan data:', error);
    throw error;
  }
};

export const fetchInventoryAutofill = async (params = {}) => {
  try {
    // Convert date string to timestamp of start of day if it exists
    const requestParams = { ...params };
    if (requestParams.transaction_date) {
      let dateObj;
      
      // Get the date object regardless of input type
      if (requestParams.transaction_date instanceof Date) {
        dateObj = new Date(requestParams.transaction_date);
      } else if (typeof requestParams.transaction_date === 'string') {
        dateObj = new Date(requestParams.transaction_date);
      } else {
        // If it's already a timestamp, convert to date first
        dateObj = new Date(requestParams.transaction_date);
      }
      
      // Set to start of day (midnight)
      dateObj.setHours(0, 0, 0, 0);
      
      // Convert back to timestamp
      requestParams.transaction_date = dateObj.getTime();
    }
    
    const response = await mandiService.get('/inventory/autofill', { params: requestParams });
    if (response.responseData) {
      return response.responseData;
    }
    return [];
  } catch (error) {
    console.error('Error fetching autofill data:', error);
    throw error;
  }
};

/**
 * Generate a dispatch challan
 * @param {Object} data - The challan data
 * @param {string} data.to_location - Destination location
 * @param {number} data.total_cases - Total number of cases
 * @param {number} data.freight_per_box - Freight cost per box
 * @param {number} data.total_freight - Total freight amount
 * @param {number} data.advance - Advance payment amount
 * @param {string} data.vehicle_number - Vehicle number
 * @param {string} data.driver_name - Driver name
 * @param {string} data.driver_contact - Driver contact number
 * @param {string} data.vehicle_owner_name - Vehicle owner name
 * @param {string} data.vehicle_type - Type of vehicle
 * @param {number} data.customer_id - Customer ID
 * @param {number} data.mandi_id - Mandi ID
 * @param {number} data.transaction_date - Transaction date timestamp
 * @returns {Promise} - API response with challan data
 */
export const generateDispatchChallan = async (data) => {
  try {
    // Basic validation
    if (!data.customer_id || !data.vehicle_number || !data.transaction_date) {
      throw new Error('Customer ID, vehicle number, and transaction date are required');
    }
    
    const response = await mandiService.post('/inventory/dispatchchallan', data);
    
    if (response.responseData) {
      return response.responseData;
    }
    return null;
  } catch (error) {
    console.error('Error generating dispatch challan:', error);
    throw error;
  }
};
