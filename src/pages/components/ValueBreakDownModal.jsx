import React from 'react';

import { Grid, Typography } from '@mui/material';
import styled from 'styled-components';

import AppLoader from 'Components/AppLoader';
import CustomModal from 'Components/Modal';
import Table from 'Components/Table';
import { capitalize, toFixedNumber } from 'Utilities';
import { calcLotSellingPriceForFarmer } from 'Utilities/calcNetAmount';
import { getCustomersName } from 'Utilities/constants';

import ProcessCharges from './ProcessCharges';

const FlexContainer = styled.div`
  div {
    display: flex;
    justify-content: space-between;
    gap: 16px;
  }
`;

const COLUMNS = [
  {
    key: 'grade',
    header: 'Grade',
    align: 'center',
    style: {
      width: '11rem'
    },
    render: ({
      rowData: { grade = '', mandi_pack_name = '', pack_color_picker = '' } = {}
    }) => (
      <>
        <Typography>{grade}</Typography>
        <Typography style={{ background: pack_color_picker }}>
          <b>{mandi_pack_name}</b>
        </Typography>
      </>
    )
  },
  {
    key: 'process',
    header: 'Processes',
    align: 'center',

    render: ({ rowData: { process_charges } = {} }) =>
      process_charges?.map(({ short_code }, index) => (
        <>
          {short_code}
          {index < process_charges.length - 1 && ', '}
        </>
      ))
  },
  {
    key: 'net_weight',
    header: 'Net Weight',
    align: 'center'
  },
  {
    key: 'units',
    header: 'Units',
    align: 'center'
  },
  {
    key: 'selling_price',
    header: 'Selling Price',
    align: 'center',
    style: {
      maxWidth: '70px'
    },
    render: ({ rowData: { selling_price_per_unit, selling_price, discount_per_unit, enable_pack_auction, auction_price_type } = {} }) => `${calcLotSellingPriceForFarmer({
      selling_price_per_unit,
      selling_price,
      discount_per_unit,
      enable_pack_auction,
      auction_price_type
    })}`
  },
  // {
  //   key: 'chute',
  //   header: 'Chute (%)',
  //   align: 'center',
  //   style: {
  //     maxWidth: '70px'
  //   },
  //   render: ({ rowData: { chute } = {} }) => `${chute ? `${chute}%` : ''}`
  // },
  {
    key: 'discount_per_unit',
    header: 'Discount',
    align: 'center',
    style: {
      maxWidth: '100px'
    },
    render: ({ rowData: { discount_per_unit } = {} }) =>
      `${discount_per_unit ? toFixedNumber(discount_per_unit) : ''}`
  },
  {
    key: '',
    header: 'Value',
    footer: 'total_value',
    render: ({ rowData: { net_weight = 0, selling_price, chute = 0 } = {} }) =>
      toFixedNumber(net_weight * selling_price * (1 - chute / 100), 2)
  },
  {
    key: 'customer_name',
    header: 'Customer Name',
    align: 'center',
    render: ({ rowData = {} }) => {
      // Handle DCL breached customers - now with API-provided fields
      if (rowData.customer_id === -5) {
        const breachedCustomerName = 
          rowData.dcl_breached_customer_name || 
          rowData.dcl_breached_customer_short_code ||
          'DCL Breached Customer';
        const breachedCustomerShortCode = rowData.dcl_breached_customer_short_code || '';
        
        return (
          <>
            {breachedCustomerName}
            {breachedCustomerShortCode && breachedCustomerName !== breachedCustomerShortCode && ` - ${breachedCustomerShortCode}`}
          </>
        );
      }
      
      // Handle regular customers
      const { name = '', customer_short_code = '' } =
        getCustomersName(rowData) || {};

      return (
        <>
          {name}
          {customer_short_code && ` - ${customer_short_code}`}
        </>
      );
    }
  }
];

const ValueBreakDownModal = ({
  open,
  closeModal,
  footerValue,
  mandiCharges,
  loading
}) => {

  const footerData = () => {
    const {
      warehousing_cost,
      discount,
      net_amount,
      negative_bill_discount = 0,
      farmer_commission,
      farmer_commission_discount,
      flat_charges,
      transportation_cost,
      deductions,
      farmer_other_charges,
      farmer_token_discounts,
      process_charge_deductions = {},
      satellite_mandi_transportation_cost = 0,
      farmer_material_charges = [],
      farmer_token_bonus = 0
    } = footerValue || {};

    const packhouse_amount = Array.isArray(deductions)
      ? deductions?.[0]?.amount || 0
      : 0;

    const farmer_charges = (farmer_other_charges || []).map(
      ({ charge_type, charge_value = 0, discount = 0 }, index) => (
        <React.Fragment key={`${charge_type}-${index}`}>
          <div>
            <div>{charge_type}</div>
            <div>-{toFixedNumber(charge_value, 2)}</div>
          </div>
          {discount > 0 && (
            <div>
              <div>Discount on {charge_type}</div>
              <div>-{toFixedNumber(discount, 2)}</div>
            </div>
          )}
        </React.Fragment>
      )
    );

    const farmer_token_discount = (farmer_token_discounts || []).map(
      ({ discount_type, discount_value = 0, discount_percent = 0 }) => (
        <div key={discount_type}>
          <div>
            {discount_type === 'ScratchCardDiscount'
              ? `PromoDiscount (${discount_percent}% Off)`
              : discount_type}
          </div>
          <div>+{toFixedNumber(discount_value, 2)}</div>
        </div>
      )
    );

    return (
      <FlexContainer>
        {farmer_commission > 0 && (
          <div>
            <div>Farmer Commission</div>
            <div>-{farmer_commission}</div>
          </div>
        )}
        {farmer_commission_discount > 0 && (
          <div>
            <div>Farmer Commission Discount</div>
            <div>{farmer_commission_discount}</div>
          </div>
        )}
        <div>
          <div>Warehousing</div>
          <div>-{warehousing_cost}</div>
        </div>
        <div>
          <div>Discount</div>
          <div>{toFixedNumber(discount, 2)}</div>
        </div>
        <div>
          <div>Transportation Cost</div>
          <div>-{toFixedNumber(transportation_cost, 2)}</div>
        </div>
        {flat_charges?.map((item, index) => (
          <div key={index}>
            <div>{capitalize(item?.charge_type)}</div>
            <div>-{item?.charge_value}</div>
          </div>
        ))}
        {!flat_charges?.length &&
          mandiCharges?.map((item, index) => (
            <div key={index}>
              <div>{capitalize(item)}</div>
              <div>{0}</div>
            </div>
          ))}
        <div>
          <div>Packhouse Bill Charges:</div>
          <div>-{toFixedNumber(packhouse_amount, 2)}</div>
        </div>
        {farmer_charges}
        {satellite_mandi_transportation_cost > 0 && (
          <div>
            <div>Satellite Mandi Transportation Cost</div>
            <div>-{toFixedNumber(satellite_mandi_transportation_cost, 2)}</div>
          </div>
        )}
        {farmer_token_discount}

        <div>
          <ProcessCharges
            process_charge_deductions={process_charge_deductions}
          />
        </div>

        {farmer_token_bonus > 0 && (
          <div>
            <div>Incentive Amount </div>
            <div>{toFixedNumber(farmer_token_bonus, 2)}</div>
          </div>
        )}

        {farmer_material_charges?.map(({ id = '', name = '', amount = 0 }) => (
          <div key={id}>
            <div>Unreturned Crates ({name})</div>
            <div>-{toFixedNumber(amount, 2)}</div>
          </div>
        ))}
        <div>
          <div>Net Amount </div>
          <div>{toFixedNumber(net_amount + negative_bill_discount, 2)}</div>
        </div>
      </FlexContainer>
    );
  };

  return (
    <CustomModal
      title='Total Value'
      open={open}
      contentSize
      onClose={closeModal}
    >
      {loading ? (
        <AppLoader />
      ) : (
        <>
          <div
            style={{
              maxHeight: '300px',
              marginBottom: '1rem',
              overflowY: 'auto'
            }}
          >
            <Table
              hover
              dataKey='id'
              columns={COLUMNS}
              header={true}
              data={footerValue.lots?.map((item) => ({
                ...item,
                reject_reason: '',
                enable_pack_auction: footerValue.enable_pack_auction || false,
                auction_price_type: footerValue.mandi_details?.auction_price_type || 'KG'
              }))}
            />
          </div>
          <div style={{ display: 'flex', justifyContent: 'flex-end' }}>
            <div style={{ width: '300px' }}>
              {footerData()}
            </div>
          </div>
        </>
      )}
    </CustomModal>
  );
};

export default ValueBreakDownModal;
