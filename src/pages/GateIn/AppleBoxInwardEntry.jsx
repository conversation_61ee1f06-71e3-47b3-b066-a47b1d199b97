import { useEffect } from 'react';

import {
  Add as AddIcon,
  Delete as DeleteIcon,
  Info as InfoIcon,
} from '@mui/icons-material';
import {
  Card,
  CardContent,
  Button,
  IconButton,
  Typography,
  Box,
} from '@mui/material';
import { useFormikContext } from 'formik';
import PropTypes from 'prop-types';

import { FieldInput, FieldSelect } from 'Components/FormFields';
import { validateRequired } from 'Utilities/formvalidation';

import { getLotsGroup, getSkuSizeMap } from './utils';

// Custom validation function for SKU quantities at row level
const validateRowHasSkuQuantities = (values, index, rowIndex) => {
  const items = values?.farmer_details?.[index]?.items?.[rowIndex]?.items || [];

  const hasValue = items.some(item => {
    const units = parseInt(item?.units) || 0;
    return units > 0;
  });

  return hasValue;
};

const AppleBoxInwardEntry = ({
  index = 0,
  products = [],
  skuSizes = [],
  skus = [],
  packTypes = [],
  disabled = false,
}) => {
  const INITIAL_ROW = {
    id: 0,
    product_id: '',
    grade: '',
    farmer_marka: '',
    lot_marka: '',
    mandi_pack_id: '',
    mandi_number: '',
    items: [], // Will contain {units: '', id: sku_id}
  };
  const { values, setFieldValue } = useFormikContext();
  const lotsGroup = getLotsGroup(skus);

  const skuSizeMap = getSkuSizeMap(skus);

  // Initialize items array for a row based on skuSizes
  const initializeRowItems = (productId, group_name) => {
    return skuSizes.map(skuSize => ({
      sku_id: skuSizeMap[productId]?.[group_name]?.[skuSize.id] || skuSize.id,
      units: '',
    }));
  };

  // Map API data to form structure based on sku_id
  const mapApiDataToFormStructure = (apiItems, productId, group_name) => {
    if (!apiItems || !Array.isArray(apiItems))
      return initializeRowItems(productId, group_name);

    // Create a map of sku_id to units from API data
    const apiDataMap = {};
    apiItems.forEach(item => {
      if (item.sku_id && item.units !== undefined) {
        apiDataMap[item.sku_id] = {
          units: item.units,
          id: item.id,
        };
      }
    });

    // Map to form structure based on skuSizes order
    const mappedItems = skuSizes.map(skuSize => {
      const expectedSkuId =
        skuSizeMap[productId]?.[group_name]?.[skuSize.id] || skuSize.id;
      const apiData = apiDataMap[expectedSkuId]; // Get the data object or undefined
      const mappedItem = {
        sku_id: expectedSkuId,
        units: apiData?.units || '',
        id: apiData?.id || '',
      };

      return mappedItem;
    });

    // Initialize rows with API data or default structure
    return mappedItems;
  };

  // Get current rows from Formik values or use initial row
  const rows = values?.farmer_details?.[index]?.items || [INITIAL_ROW];

  // Get farmer marka from farmer level (computed from first lot or user input)
  const farmerMarka = values?.farmer_details?.[index]?.farmer_marka || '';

  // Calculate total boxes for a row
  const calculateTotalBoxes = (items = []) => {
    if (!Array.isArray(items)) return 0;
    return items.reduce((sum, item) => {
      if (!item || typeof item.units === 'undefined') return sum;
      return sum + (parseInt(item.units) || 0);
    }, 0);
  };

  // Add new row
  const addRow = () => {
    const newRow = {
      product_id: '',
      grade: '',
      farmer_marka: farmerMarka, // Use farmer marka for new rows
      lot_marka: '',
      mandi_pack_id: '',
      mandi_number: '',
      items: [],
    };
    const updatedRows = [...rows, newRow];
    setFieldValue(`farmer_details.${index}.items`, updatedRows);
  };

  // Initialize items when product is selected
  const handleProductChange = (rowIndex, productId) => {
    setFieldValue(
      `farmer_details.${index}.items.${rowIndex}.product_id`,
      productId
    );
    if (productId) {
      // Clear grade and items when product changes
      setFieldValue(`farmer_details.${index}.items.${rowIndex}.grade`, '');
      setFieldValue(`farmer_details.${index}.items.${rowIndex}.items`, []);
    } else {
      setFieldValue(`farmer_details.${index}.items.${rowIndex}.items`, []);
    }
  };

  const handleGradeChange = (rowIndex, productId, grade) => {
    setFieldValue(`farmer_details.${index}.items.${rowIndex}.grade`, grade);

    if (productId && grade) {
      // Get current quantities
      const currentItems = values?.farmer_details?.[index]?.items?.[rowIndex]?.items || [];
      
      // Create new items with updated SKU IDs but preserve quantities and IDs by index position
      const newItems = skuSizes.map((skuSize, idx) => {
        const newSkuId = skuSizeMap[productId]?.[grade]?.[skuSize.id] || skuSize.id;
        const existingItem = currentItems[idx] || {};
        
        return {
          sku_id: newSkuId,
          units: existingItem.units || '', // Preserve quantity by index position
          ...(existingItem.id && { id: existingItem.id }), // Preserve existing ID if it exists
        };
      });

      setFieldValue(
        `farmer_details.${index}.items.${rowIndex}.items`,
        newItems
      );
    } else {
      setFieldValue(`farmer_details.${index}.items.${rowIndex}.items`, []);
    }
  };

  // Remove row
  const removeRow = rowIndex => {
    if (rows?.length > 1) {
      const updatedRows = rows.filter((_, idx) => idx !== rowIndex);
      setFieldValue(`farmer_details.${index}.items`, updatedRows);
    }
  };

  // Initialize formik values
  useEffect(() => {
    if (!values?.farmer_details?.[index]?.items) {
      setFieldValue(`farmer_details.${index}.items`, [INITIAL_ROW]);
    } else {
      // Ensure existing rows have properly initialized items arrays
      const currentItems = values.farmer_details[index].items;
      const updatedItems = currentItems.map(item => {
        if (item.product_id && item.grade) {
          // If items exist (from API), map them properly based on sku_id
          if (item.items && item.items.length > 0) {
            return {
              ...item,
              items: mapApiDataToFormStructure(
                item.items,
                item.product_id,
                item.grade
              ),
            };
          }
          // If no items, initialize empty structure
          return {
            ...item,
            items: initializeRowItems(item.product_id, item.grade),
          };
        }
        return item;
      });

      // Only update if there were changes
      const hasChanges = updatedItems.some(
        (item, idx) =>
          JSON.stringify(item) !== JSON.stringify(currentItems[idx])
      );

      if (hasChanges) {
        setFieldValue(`farmer_details.${index}.items`, updatedItems);
      }
    }
  }, [
    index,
    setFieldValue,
    values?.farmer_details,
    skuSizes.length,
    INITIAL_ROW,
    initializeRowItems,
    mapApiDataToFormStructure,
  ]);

  // Note: Automatic propagation removed - now using manual Apply button at farmer level

  return (
    <Box sx={{ mt: 2, mb: 2 }}>
      {rows?.map((row, rowIndex) => (
        <Card
          key={rowIndex}
          sx={{
            mb: 3,
            border: '1px solid',
            borderColor: 'divider',
            borderRadius: 2,
            boxShadow: 1,
            '&:hover': {
              boxShadow: 2,
              borderColor: 'primary.light',
            },
            transition: 'all 0.2s ease-in-out',
            overflow: 'hidden',
          }}
        >
          <CardContent sx={{ p: { xs: 2, sm: 3 }, '&:last-child': { pb: { xs: 2, sm: 3 } } }}>
            {/* Header Section with Mandi Number and Actions */}
            <Box
              sx={{
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
                mb: 3,
                flexWrap: 'wrap',
                gap: 2,
              }}
            >
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <Typography
                  variant='subtitle1'
                  sx={{
                    fontWeight: 600,
                    color: 'text.primary',
                    fontSize: { xs: '1rem', sm: '1.1rem' },
                  }}
                >
                  Mandi No:
                </Typography>
                {row.mandi_number ? (
                  <Typography
                    sx={{
                      fontWeight: 600,
                      color: 'primary.main',
                      fontSize: { xs: '1rem', sm: '1.1rem' },
                    }}
                    title={row.mandi_number}
                  >
                    {row.mandi_number}
                  </Typography>
                ) : (
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <Typography
                      sx={{
                        fontWeight: 500,
                        color: 'text.secondary',
                        fontSize: '0.875rem',
                        fontStyle: 'italic',
                      }}
                    >
                      Yet to be generated
                    </Typography>
                    <InfoIcon
                      sx={{
                        fontSize: '1rem',
                        color: 'text.secondary',
                        cursor: 'help',
                      }}
                      title='Mandi number will be generated post submission'
                    />
                  </Box>
                )}
              </Box>

              {/* Delete Button */}
              {rows.length > 1 && (
                <IconButton
                  onClick={() => removeRow(rowIndex)}
                  disabled={disabled && row.mandi_number}
                  sx={{
                    color: 'error.main',
                    '&:hover': {
                      backgroundColor: 'error.light',
                      color: 'error.contrastText',
                    }
                  }}
                >
                  <DeleteIcon />
                </IconButton>
              )}
            </Box>

            {/* Form Fields Section */}
            <Box
              sx={{
                display: 'flex',
                flexDirection: { xs: 'column', lg: 'row' },
                gap: { xs: 3, lg: 2 },
                mb: 3,
              }}
            >
              {/* Basic Information Fields */}
              <Box
                sx={{
                  display: 'grid',
                  gridTemplateColumns: {
                    xs: '1fr',
                    sm: 'repeat(2, 1fr)',
                    md: 'repeat(3, 1fr)',
                    lg: 'repeat(5, 1fr)',
                  },
                  gap: { xs: 2, sm: 2, lg: 1.5 },
                  flex: { xs: 1, lg: 2 },
                }}
              >
                {/* Product Field */}
                <Box>
                  <FieldSelect
                    name={`farmer_details.${index}.items.${rowIndex}.product_id`}
                    label='Product'
                    options={products.map(product => ({
                      value: product.id,
                      text: product.name || product.code,
                    }))}
                    disabled={disabled && row.mandi_number}
                    validate={validateRequired}
                    size='small'
                    showNone={false}
                    fullWidth
                    InputLabelProps={{
                      shrink: true,
                      required: true,
                    }}
                    onChange={e =>
                      handleProductChange(rowIndex, e.target.value)
                    }
                    required
                  />
                </Box>

                {/* Pack Type Field */}
                <Box>
                  <FieldSelect
                    name={`farmer_details.${index}.items.${rowIndex}.mandi_pack_id`}
                    label='Pack Type'
                    size='small'
                    fullWidth
                    disabled={disabled && row.mandi_number}
                    showNone={false}
                    options={packTypes.map(type => ({
                      value: type.id,
                      text: type.mandi_packaging_type,
                    }))}
                    InputLabelProps={{
                      shrink: true,
                      required: true,
                    }}
                    validate={validateRequired}
                    required
                  />
                </Box>

                {/* Lot Field */}
                <Box>
                  <FieldSelect
                    name={`farmer_details.${index}.items.${rowIndex}.grade`}
                    label='Lot'
                    size='small'
                    fullWidth
                    disabled={disabled && row.mandi_number}
                    showNone={false}
                    options={
                      lotsGroup?.map(lot => ({
                        value: lot,
                        text: lot,
                      })) || []
                    }
                    onChange={e =>
                      handleGradeChange(
                        rowIndex,
                        row.product_id,
                        e.target.value
                      )
                    }
                    InputLabelProps={{
                      shrink: true,
                      required: true,
                    }}
                    validate={validateRequired}
                    required
                  />
                </Box>

                {/* Farmer Marka Field */}
                <Box>
                  <FieldInput
                    name={`farmer_details.${index}.items.${rowIndex}.farmer_marka`}
                    label='Farmer Marka'
                    placeholder='Farmer Marka'
                    type='text'
                    size='small'
                    variant='outlined'
                    fullWidth
                    InputLabelProps={{
                      shrink: true,
                    }}
                  />
                </Box>

                {/* Lot Marka Field */}
                <Box>
                  <FieldInput
                    name={`farmer_details.${index}.items.${rowIndex}.lot_marka`}
                    label='Lot Marka'
                    placeholder='Lot Marka'
                    type='text'
                    size='small'
                    variant='outlined'
                    fullWidth
                    InputLabelProps={{
                      shrink: true,
                    }}
                  />
                </Box>
              </Box>

            </Box>

            {/* SKU Quantities Section */}
            <Box sx={{ mt: 2 }}>
              <Typography
                variant='subtitle1'
                sx={{
                  mb: 2,
                  fontWeight: 600,
                  color: 'text.primary',
                  fontSize: { xs: '1rem', sm: '1.1rem' },
                }}
              >
                SKU Quantities
              </Typography>
              <Box
                sx={{
                  p: { xs: 1.5, sm: 2 },
                  backgroundColor: 'grey.50',
                  borderRadius: 1,
                  border: '1px solid',
                  borderColor: 'divider',
                }}
              >
                {/* Header Row with SKU Labels */}
                <Box
                  sx={{
                    display: 'grid',
                    gridTemplateColumns: `repeat(${skuSizes.length}, 1fr)`,
                    gap: { xs: 0.5, sm: 1 },
                    mb: 1,
                  }}
                >
                  {skuSizes.map((skuSize, idx) => (
                    <Typography
                      key={idx}
                      variant='caption'
                      sx={{
                        fontSize: { xs: '0.65rem', sm: '0.7rem', md: '0.75rem' },
                        fontWeight: 600,
                        color: 'text.primary',
                        textAlign: 'center',
                        lineHeight: 1.3,
                        whiteSpace: 'normal', // Allow wrapping
                        wordBreak: 'break-word', // Break long words if needed
                        px: 0.5,
                        py: 0.5,
                        minHeight: { xs: '2.5rem', sm: '2.2rem', md: '2rem' }, // Ensure consistent height
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                      }}
                    >
                      {skuSize?.size}
                    </Typography>
                  ))}
                </Box>

                {/* Input Row */}
                <Box
                  sx={{
                    display: 'grid',
                    gridTemplateColumns: `repeat(${skuSizes.length}, 1fr)`,
                    gap: { xs: 0.5, sm: 1 },
                  }}
                >
                  {skuSizes.map((skuSize, idx) => (
                    <FieldInput
                      key={idx}
                      name={`farmer_details.${index}.items.${rowIndex}.items.${idx}.units`}
                      type='number'
                      size='small'
                      disabled={!row.product_id}
                      value={row?.items?.[idx]?.units || ''}
                      validate={() =>
                        !validateRowHasSkuQuantities(
                          values,
                          index,
                          rowIndex
                        )
                      }
                      onChange={e => {
                        const currentRow = rows[rowIndex];
                        if (currentRow?.product_id) {
                          setFieldValue(
                            `farmer_details.${index}.items.${rowIndex}.items.${idx}.sku_id`,
                            skuSizeMap[currentRow.product_id]?.[
                              currentRow.grade
                            ]?.[skuSize.id] || skuSize.id
                          );
                        }
                        setFieldValue(
                          `farmer_details.${index}.items.${rowIndex}.items.${idx}.units`,
                          e.target.value
                        );
                      }}
                      fullWidth
                      inputProps={{
                        min: 0,
                        style: {
                          textAlign: 'center',
                          fontSize: '0.875rem',
                        },
                      }}
                      sx={{
                        '& .MuiOutlinedInput-root': {
                          backgroundColor: 'background.paper',
                          '& fieldset': {
                            borderColor: 'divider',
                          },
                          '&:hover fieldset': {
                            borderColor: 'primary.main',
                          },
                          '&.Mui-focused fieldset': {
                            borderColor: 'primary.main',
                          },
                        },
                        '& input[type=number]': {
                          MozAppearance: 'textfield',
                        },
                        '& input[type=number]::-webkit-outer-spin-button': {
                          WebkitAppearance: 'none',
                          margin: 0,
                        },
                        '& input[type=number]::-webkit-inner-spin-button': {
                          WebkitAppearance: 'none',
                          margin: 0,
                        },
                      }}
                    />
                  ))}
                </Box>
              </Box>

              {/* Validation Error Display */}
              {!validateRowHasSkuQuantities(values, index, rowIndex) &&
                row.product_id &&
                row.grade && (
                  <Typography
                    variant='caption'
                    sx={{
                      color: 'error.main',
                      fontSize: '0.75rem',
                      mt: 1,
                      display: 'block',
                      textAlign: 'center',
                    }}
                  >
                    At least one SKU quantity is required
                  </Typography>
                )}
            </Box>

            {/* Total Boxes Section */}
            <Box
              sx={{
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
                mt: 3,
                pt: 2,
                borderTop: '1px solid',
                borderColor: 'divider',
              }}
            >
              <Typography
                variant='h6'
                sx={{
                  fontWeight: 600,
                  color: calculateTotalBoxes(row?.items || []) > 0 ? 'primary.main' : 'error.main',
                  fontSize: { xs: '1.1rem', sm: '1.25rem' },
                }}
              >
                Total Boxes: {calculateTotalBoxes(row?.items || [])}
              </Typography>
            </Box>
          </CardContent>
        </Card>
      ))}

      <Box sx={{ mt: 2 }}>
        <Button
          startIcon={<AddIcon />}
          onClick={addRow}
          variant='outlined'
          size='small'
        >
          Add Another Mandi
        </Button>
      </Box>
    </Box>
  );
};

AppleBoxInwardEntry.propTypes = {
  index: PropTypes.number,
  products: PropTypes.array,
  skuSizes: PropTypes.array,
  skus: PropTypes.array,
  packTypes: PropTypes.array,
  disabled: PropTypes.bool,
  naValue: PropTypes.string,
};

export default AppleBoxInwardEntry;
