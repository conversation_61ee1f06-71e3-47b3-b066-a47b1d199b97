import React, { useEffect, useState } from 'react';

import { Cancel as CancelIcon, Refresh as RefreshIcon } from '@mui/icons-material';
import { Box, Button, IconButton, Typography } from '@mui/material';
import { FieldArray, useFormikContext } from 'formik';
import { debounce } from 'lodash';

import { useSiteValue } from 'App/SiteContext';
import { Modal as CustomModal } from 'Components';
import { FieldCombo, FieldInput } from 'Components/FormFields';
import FarmerRegistrationForm from 'Pages/Registration/Farmer';
import { getPartners, getPendingCrates } from 'Services/purchaseOrder';
import { getPartnerDetails } from 'Services/register';
import { filterOptions } from 'Utilities';
import { INWARD_TYPE } from 'Utilities/constants';
import { validateRequired } from 'Utilities/formvalidation';
// import { ReceiveCratesPopup } from 'vg-library';

import useAtomicStyles from '../../theme/AtomicCss';

import AppleBoxInwardEntry from './AppleBoxInwardEntry';
import CancelTokenModal from './CancelTokenModal.jsx';
import FarmerStatus from './FarmerStatus.jsx';

const FarmerDetails = ({
  index,
  gatein_id = '',
  products = [],
  skuSizes = [],
  packTypes = [],
  skus = [],
  isGrossRequired = false,
  showCrossIcon = false,
  removeFarmerDetails = () => {},
  disabled,
}) => {
  const [farmers, setFarmers] = useState([]);
  const { values, setFieldValue } = useFormikContext();
  const [open, setOpen] = useState(false);
  const [modalOpen, setModalOpen] = useState(false);
  const [farmerStatus, setFarmerStatus] = useState({});

  const getUpdatedFarmers = query => {
    getPartners({ q: query }).then(({ items = [] }) => {
      setFarmers(items);
    });
  };

  useEffect(() => {
    if (products.length === 1) {
      setFieldValue('farmer_details[0].items[0].product', products[0]);
    }
  }, [products]);

  // Initialize farmer marka from first lot's marka when editing
  useEffect(() => {
    const farmerDetail = values?.farmer_details?.[index];
    const firstLotMarka = farmerDetail?.items?.[0]?.farmer_marka;

    // If we're in edit mode (gatein_id exists) and farmer marka is empty but first lot has marka
    if (gatein_id && firstLotMarka && !farmerDetail?.farmer_marka) {
      setFieldValue(`farmer_details.${index}.farmer_marka`, firstLotMarka);
    }
  }, [gatein_id, values?.farmer_details?.[index]?.items, index, setFieldValue]);

  const handleFarmerDetails = ({ id }) => {
    getPartnerDetails(id).then(res =>
      setFieldValue(`farmer_details.${index}.farmer`, res)
    );
  };

  const toggleModal = () => {
    setOpen(!open);
  };

  const selectedProducts = values?.farmer_details?.map(
    ({ items = [] }) =>
      items?.map(({ product = {} }) => product?.id || '') || []
  );

  const cancelToggleModal = () => {
    setModalOpen(!modalOpen);
  };

  const handleStatus = values => {
    setFarmerStatus(values);
  };

  // Function to apply farmer marka to all lots
  const applyFarmerMarkaToAllLots = () => {
    const farmerMarka = values?.farmer_details?.[index]?.farmer_marka;
    const items = values?.farmer_details?.[index]?.items || [];

    if (farmerMarka && items.length > 0) {
      const updatedItems = items.map(item => ({
        ...item,
        farmer_marka: farmerMarka
      }));

      setFieldValue(`farmer_details.${index}.items`, updatedItems);
    }
  };

  const farmerDetails = values.farmer_details[index]?.farmer;

  return (
    <Box
      sx={{
        position: 'relative',
        p: { xs: 2, sm: 3 },
        backgroundColor: 'background.paper',
        borderRadius: 2,
        boxShadow: 1,
        mb: 2,
        border: '1px solid',
        borderColor: 'divider',
      }}
    >
      {/* Header Section with Token and Actions */}
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          mb: 3,
          flexWrap: 'wrap',
          gap: 2,
        }}
      >
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          {values?.farmer_details?.[index]?.token && (
            <Typography
              variant='h5'
              component='span'
              sx={{
                fontWeight: 600,
                color: 'primary.main',
                fontSize: { xs: '1.25rem', sm: '1.5rem' },
              }}
            >
              {values?.farmer_details?.[index]?.token}
            </Typography>
          )}

          {values?.farmer_details?.[index].is_cancellable &&
            !values?.farmer_details?.[index].is_cancelled && (
              <Button
                color='error'
                size='small'
                variant='contained'
                onClick={cancelToggleModal}
                data-cy={`mandi.gateIn.farmerDetails.cancelToken.${index}`}
                sx={{
                  textTransform: 'none',
                  borderRadius: 1.5,
                  px: 2,
                  py: 0.5,
                }}
              >
                Cancel Token
              </Button>
            )}
        </Box>

        {showCrossIcon && (
          <IconButton
            onClick={removeFarmerDetails}
            data-cy={`mandi.gateIn.farmerDetails.cancelFarmerDetails.${index}`}
            sx={{
              color: 'error.main',
              '&:hover': {
                backgroundColor: 'error.light',
                color: 'error.contrastText',
              }
            }}
          >
            <CancelIcon />
          </IconButton>
        )}
      </Box>
      {/* Form Fields Section */}
      <Box
        sx={{
          display: 'flex',
          flexDirection: { xs: 'column', lg: 'row' },
          gap: { xs: 3, lg: 2 },
          alignItems: { xs: 'stretch', lg: 'flex-end' },
          mb: 3,
        }}
      >
        {/* Farmer Search Field */}
        <Box sx={{ flex: { xs: 1, lg: 2 }, minWidth: 0 }}>
          <FieldCombo
            name={`farmer_details.${index}.farmer`}
            label='Contact Number/Name'
            placeholder='Search Farmer'
            size='small'
            variant='outlined'
            options={farmers || []}
            onChangeInput={debounce(q => getUpdatedFarmers(q), 300)}
            filterOptions={filterOptions}
            validate={validateRequired}
            optionLabel={({ name = '', phone_number = '' }) =>
              `${name}-${phone_number}`
            }
            onChange={e => {
              handleStatus(e);
            }}
            fullWidth
            InputLabelProps={{
              required: true,
              shrink: true,
            }}
            disabled={disabled}
            inputProps={{
              'data-cy': `mandi.gateIn.farmerDetails.searchFarmer.${index}`,
            }}
          />
        </Box>

        {/* Farmer Marka Field with Apply Button */}
        <Box
          sx={{
            display: 'flex',
            alignItems: 'flex-end',
            gap: 1,
            flex: { xs: 1, lg: 'none' },
            minWidth: 0,
          }}
        >
          <Box sx={{ minWidth: { xs: 0, lg: '140px' }, flex: 1 }}>
            <FieldInput
              name={`farmer_details.${index}.farmer_marka`}
              label='Farmer Marka'
              placeholder='Farmer Marka'
              type='text'
              size='small'
              variant='outlined'
              fullWidth
              InputLabelProps={{
                shrink: true,
              }}
            />
          </Box>

          <IconButton
            size='small'
            onClick={() => applyFarmerMarkaToAllLots()}
            title="Apply farmer marka to all lots"
            sx={{
              color: 'primary.main',
              backgroundColor: 'primary.light',
              borderRadius: 1,
              p: 1,
              '&:hover': {
                backgroundColor: 'primary.main',
                color: 'primary.contrastText',
              },
              '&:disabled': {
                color: 'action.disabled',
                backgroundColor: 'action.hover',
              }
            }}
          >
            <RefreshIcon fontSize='small' />
          </IconButton>
        </Box>

        {/* Add Vendor Button */}
        <Box sx={{ flex: { xs: 1, lg: 'none' }, minWidth: 0 }}>
          <Button
            variant='outlined'
            color='primary'
            size='small'
            onClick={toggleModal}
            data-cy={`mandi.gateIn.farmerDetails.addVendor.${index}`}
            fullWidth
            sx={{
              textTransform: 'none',
              fontWeight: 500,
              borderRadius: 1.5,
              px: 2,
              py: 1,
              '&:hover': {
                backgroundColor: 'primary.light',
                color: 'primary.contrastText',
              },
            }}
            disabled={disabled}
          >
            + Add Vendor
          </Button>
        </Box>
      </Box>

      {/* Farmer Status */}
      {farmerStatus?.id && (
        <Box sx={{ mb: 2 }}>
          <FarmerStatus farmerStatus={farmerStatus} />
        </Box>
      )}

      {/* Modal for Adding Vendor */}
      <CustomModal
        title='Add Vendor'
        open={open}
        onClose={toggleModal}
        dataCy={{ 'data-cy': 'mandi.farmerRegistration.closeModal' }}
      >
        <FarmerRegistrationForm
          toggleModal={toggleModal}
          handleFarmerDetails={handleFarmerDetails}
          isModal
        />
      </CustomModal>

      {/* Apple Box Inward Entry Section */}
      <Box sx={{ mt: 2 }}>
        <AppleBoxInwardEntry
          index={index}
          products={products}
          skuSizes={skuSizes}
          packTypes={packTypes}
          skus={skus}
          gatein_id={gatein_id}
          disabled={!!gatein_id}
        />
      </Box>

      {/* Cancel Token Modal */}
      <CancelTokenModal
        data={values?.farmer_details?.[index]}
        open={modalOpen}
        toggleModal={cancelToggleModal}
      />
    </Box>
  );
};

export default FarmerDetails;
