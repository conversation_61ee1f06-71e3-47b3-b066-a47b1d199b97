import { useEffect, useState } from 'react';

import queryString from 'query-string';
import { useLocation } from 'react-router-dom';

import { useSiteValue } from 'App/SiteContext';
import useNotify from 'Hooks/useNotify';
import { getFarmerAuctionLots } from 'Services/farmerLots';
import { getSkuList } from 'Services/regrade';
import { getAuctionCustomer } from 'Services/users';

import AuctionDetailCapture from './components/AuctionDetailCapture';
import TokenInput from './components/TokenInput';
import { AUCTION_GRADING_STEPS } from './const';
import {
  formatLotsData,
  getCustomerShortCodes,
  formatMandiNumberData,
} from './utils';

const AuctionGrading = () => {
  const location = useLocation();
  const state = location.state || {};

  const notify = useNotify();
  const { mandiId, auctionDate, setAuctionDate } = useSiteValue();
  const [customers, setCustomers] = useState([]);
  const [details, setDetails] = useState([]);
  const [mandiNumberInfo, setMandiNumberInfo] = useState([]);
  const [loading, setLoading] = useState(false);
  const [skuData, setSkuData] = useState([]);
  const [initialToken, setInitialToken] = useState('');
  const [hasUrlParams, setHasUrlParams] = useState(false);

  // Determine initial step based on URL parameters
  const getInitialStep = () => {
    const urlParams = queryString.parse(location.search);
    // Start with TOKEN_INPUT initially, we'll switch after data is loaded
    return AUCTION_GRADING_STEPS.TOKEN_INPUT;
  };

  const [step, setStep] = useState(getInitialStep());
  const [token, setToken] = useState('');

  // Parse URL parameters on component mount
  useEffect(() => {
    const urlParams = queryString.parse(location.search);

    if (urlParams.token && urlParams.auction_date) {
      setHasUrlParams(true);
      setInitialToken(urlParams.token);
      setToken(urlParams.token); // Set token immediately

      const rawDate = urlParams.auction_date;
      let parsedDate;

      try {
        // If it's already a timestamp
        if (!isNaN(rawDate) && rawDate.length > 10) {
          parsedDate = parseInt(rawDate);
        } else {
          // Parse as date string
          parsedDate = new Date(rawDate).getTime();
        }

        if (!isNaN(parsedDate)) {
          setAuctionDate(parsedDate);
        }
      } catch (error) {
        // Silent error handling
      }
    } else {
      setHasUrlParams(false);
    }
  }, [location.search, setAuctionDate]);

  // Auto-submit when URL params are present and all data is loaded
  useEffect(() => {
    if (
      hasUrlParams &&
      initialToken &&
      auctionDate &&
      mandiId &&
      customers.length > 0 &&
      skuData.length > 0 &&
      step === AUCTION_GRADING_STEPS.TOKEN_INPUT &&
      details.length === 0
    ) {
      // Fetch auction data immediately - this will set step to AUCTION_DETAIL_CAPTURE when complete
      handleSubmit(initialToken);
    }
  }, [
    hasUrlParams,
    initialToken,
    auctionDate,
    mandiId,
    customers,
    skuData,
    step,
    details,
  ]);

  useEffect(() => {
    getAuctionCustomer({ mandi_id: mandiId }).then(res => {
      setCustomers(getCustomerShortCodes(res.responseData, mandiId));
    });

    // Fetch SKU data for the mandi
    getSkuList(mandiId)
      .then(res => {
        setSkuData(res.items || []);
      })
      .catch(error => {
        console.error('Error fetching SKU data:', error);
      });
  }, [mandiId]);

  const handleSubmit = token => {
    setLoading(true);
    getFarmerAuctionLots({
      token: `C-${token}`,
      auction_date: auctionDate,
      mandi_id: mandiId,
      include_discount_view: true,
    })
      .then(res => {
        console.log('API Response:', res);
        console.log('SKU Data:', skuData);

        try {
          const lots = formatLotsData(res.responseData, skuData);
          console.log('Formatted lots:', lots);

          const mandiNumberInfo = formatMandiNumberData(res.responseData, lots);
          console.log('Formatted mandi number info:', mandiNumberInfo);

          if (lots.length === 0) {
            notify('No Lots Available', 'error');
          } else {
            setToken(token);
            setDetails(mandiNumberInfo);
            setMandiNumberInfo(
              Object.values(res.responseData)[0]?.mandi_number_details || {}
            );
            setStep(AUCTION_GRADING_STEPS.AUCTION_DETAIL_CAPTURE);
          }
        } catch (processingError) {
          console.error('Error processing data:', processingError);
          notify('Error processing auction data', 'error');
        }
      })
      .catch(apiError => {
        console.error('API Error:', apiError);
        notify('Error fetching auction lots', 'error');
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const handleCancel = () => {
    setStep(AUCTION_GRADING_STEPS.TOKEN_INPUT);
    setToken('');
    setLoading(false);
    setHasUrlParams(false); // Reset URL params state so TokenInput shows
    setDetails([]); // Clear details
    setInitialToken(''); // Clear initial token
  };

  return (
    <>
      {step === AUCTION_GRADING_STEPS.TOKEN_INPUT && !hasUrlParams && (
        <TokenInput
          onSubmit={handleSubmit}
          loading={loading}
          initialToken={initialToken}
        />
      )}
      {step === AUCTION_GRADING_STEPS.AUCTION_DETAIL_CAPTURE && (
        <AuctionDetailCapture
          token={token}
          customers={customers}
          lotsInfo={details}
          mandiNumberInfo={mandiNumberInfo}
          skuData={skuData}
          handleCancel={handleCancel}
        />
      )}
    </>
  );
};

export default AuctionGrading;
