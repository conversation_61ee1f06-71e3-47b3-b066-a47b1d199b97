import { lazy } from 'react';

import { Route, Routes, Navigate } from 'react-router-dom';

const EditTransporterRegistrationForm = lazy(
  () => import('./Transporter/EditTransporterRegistrationForm')
);
const ServiceProviderRegistrationForm = lazy(() => import('./ServiceProvider'));
const TransporterRegistrationForm = lazy(() => import('./Transporter'));

const EditGraderRegistrationForm = lazy(
  () => import('./Grader/EditGraderRegistrationForm')
);

const EditFarmerRegistrationForm = lazy(
  () => import('./Farmer/EditFarmerRegistrationForm')
);
const EditServiceProviderRegistrationForm = lazy(
  () => import('./ServiceProvider/EditServiceProviderRegistrationForm')
);
const EditSupplierRegistrationForm = lazy(
  () => import('./Supplier/EditSupplierRegistrationForm')
);
const CustomerRegistrationForm = lazy(() => import('./Customer'));
const FarmerRegistrationForm = lazy(() => import('./Farmer'));
const GraderRegistrationForm = lazy(() => import('./Grader'));
const SupplierRegistrationForm = lazy(() => import('./Supplier'));

const Registration = () => (
  <Routes>
    <Route path='' element={<Navigate to='farmer' replace />} />
    <Route path='grader/:id' element={<EditGraderRegistrationForm />} />
    <Route path='grader' element={<GraderRegistrationForm />} />
    <Route path='customer' element={<CustomerRegistrationForm />} />
    <Route path='farmer/:id' element={<EditFarmerRegistrationForm />} />
    <Route path='farmer' element={<FarmerRegistrationForm />} />
    <Route path='supplier/:id' element={<EditSupplierRegistrationForm />} />
    <Route path='supplier' element={<SupplierRegistrationForm />} />
    <Route
      path='service_provider/:id'
      element={<EditServiceProviderRegistrationForm />}
    />
    <Route
      path='service-provider'
      element={<ServiceProviderRegistrationForm />}
    />
    <Route
      path='transporter/:id'
      element={<EditTransporterRegistrationForm />}
    />
    <Route path='transporter' element={<TransporterRegistrationForm />} />
  </Routes>
);

export default Registration;
