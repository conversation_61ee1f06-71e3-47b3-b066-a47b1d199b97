import { useState } from 'react';

import {
  InfoRounded,
  Upload,
  Visibility as VisibilityIcon,
  Download as DownloadIcon,
  Receipt as ReceiptIcon,
} from '@mui/icons-material';
import {
  Box,
  Button,
  Dialog,
  DialogActions,
  DialogTitle,
  Grid,
  IconButton,
  Link,
  Typography,
} from '@mui/material';
import { useNavigate } from 'react-router-dom';

import { AppButton } from 'Components';
import ImageIcons from 'Components/AppIcons/ImageIcons.jsx';
import CreditBreachTag from 'Components/CreditBreachTag';
import { getCreditBreachInfo } from 'Pages/AuctionGrading/utils';
import { confirmRecordArrival } from 'Services/token';
import { toFixedNumber } from 'Utilities';
import { INWARD_TYPE, INWARD_TYPE_PREFIX } from 'Utilities/constants';
import { GATEIN_TYPE, TOKEN_TABS } from 'Utilities/constants/lots';
import { getFormattedDate, getFormattedTime24Hours } from 'Utilities/dateUtils';

import useNotify from '../../hooks/useNotify';
import useRoleBasedAccess from '../../hooks/useRoleBasedAccess';

import { CustomTooltip, UnloadingData } from './Styled';

export const TokenCol = ({
  tab = '',
  data = '',
  gatein_id = '',
  auction_date = '',
  inward_type = '',
  mandi_type = '',
  satellite_name = '',
  rowData = {},
  onDownloadPriceSlip = () => {},
  onDownloadCustomerSlip = () => {},
}) => {
  const noLinkTabs = [
    TOKEN_TABS.PR_RAISED,
    TOKEN_TABS.PAID,
    TOKEN_TABS.PR_APPROVED,
    TOKEN_TABS.CANCELLED,
    TOKEN_TABS.SELF_SOLD,
  ];

  const showPriceSlipDownload = [
    TOKEN_TABS.AUCTION_READY,
    TOKEN_TABS.SOLD,
    TOKEN_TABS.PR_RAISED,
    TOKEN_TABS.PR_APPROVED,
    TOKEN_TABS.FINANCE_APPROVED,
    TOKEN_TABS.SCHEDULED_TO_PAY,
    TOKEN_TABS.PAID,
  ].includes(tab);

  const showCustomerSlipDownload = [
    TOKEN_TABS.SOLD,
    TOKEN_TABS.PR_RAISED,
    TOKEN_TABS.PR_APPROVED,
    TOKEN_TABS.FINANCE_APPROVED,
    TOKEN_TABS.SCHEDULED_TO_PAY,
    TOKEN_TABS.PAID,
  ].includes(tab);

  const { unloading_time = '' } = rowData || {};

  // Check for credit breach customers in lots_data
  const creditBreachInfo = getCreditBreachInfo(rowData?.lots_data || []);

  const handlePriceSlipDownload = () => {
    onDownloadPriceSlip(rowData);
  };

  const handleCustomerSlipDownload = () => {
    onDownloadCustomerSlip(rowData);
  };

  return (
    <div
      style={{
        display: 'flex',
        flexDirection: 'column',
        gap: '4px',
        padding: '4px 0',
      }}
    >
      <div style={{ display: 'flex', alignItems: 'center', gap: '6px' }}>
        <ImageIcons
          name={inward_type?.toLowerCase()}
          style={{ width: '16px', height: '16px', flexShrink: 0 }}
        />
        <div style={{ minWidth: 0, flex: 1 }}>
          {noLinkTabs.includes(tab) ? (
            <Typography variant='body2' fontSize='0.8rem' noWrap>
              {data}
            </Typography>
          ) : (
            <Typography
              component='a'
              color='primary'
              variant='body2'
              fontSize='0.8rem'
              noWrap
              href={`/app/gate-in/edit/${gatein_id}?auction_date=${auction_date}${
                tab === TOKEN_TABS.SOLD && '&sold=true'
              }`}
              style={{ textDecoration: 'none' }}
            >
              {data}
            </Typography>
          )}
        </div>
        <div
          style={{
            display: 'flex',
            alignItems: 'center',
            gap: '2px',
            flexShrink: 0,
          }}
        >
          {showPriceSlipDownload && (
            <IconButton
              size='small'
              onClick={handlePriceSlipDownload}
              title='Price Slip'
              sx={{ padding: '2px', minWidth: '24px', height: '24px' }}
            >
              <DownloadIcon fontSize='small' color='primary' />
            </IconButton>
          )}
          {showCustomerSlipDownload && (
            <IconButton
              size='small'
              onClick={handleCustomerSlipDownload}
              title='Download Customer Slip'
              sx={{ padding: '2px', minWidth: '24px', height: '24px' }}
            >
              <ReceiptIcon fontSize='small' color='secondary' />
            </IconButton>
          )}
        </div>
      </div>

      <div
        style={{
          display: 'flex',
          flexDirection: 'column',
          gap: '2px',
          marginLeft: '22px',
        }}
      >
        <Typography
          component='div'
          variant='caption'
          fontSize='0.7rem'
          color='textSecondary'
        >
          <b>{getFormattedDate(auction_date)}</b>
        </Typography>
        {satellite_name && (
          <Typography color='primary' variant='caption' fontSize='0.7rem'>
            <b>{satellite_name}</b>
          </Typography>
        )}
        {unloading_time !== 0 && (
          <UnloadingData>
            <Upload style={{ padding: '1px', fontSize: '12px' }} />
            <Typography
              variant='caption'
              fontSize='0.7rem'
              sx={{ fontWeight: 'bold' }}
            >
              {getFormattedTime24Hours(unloading_time)}
            </Typography>
          </UnloadingData>
        )}
        {creditBreachInfo && (
          <Box sx={{ mt: 0.5 }}>
            <CreditBreachTag breachedCustomers={creditBreachInfo} />
          </Box>
        )}
      </div>
    </div>
  );
};

export const FarmerCol = ({ rowData, getPartnerStatusUI }) => {
  const { farmer_name, farmer_address, farmer_id, lots_data } = rowData;
  const { gatein_type } = lots_data?.[0];

  return (
    <div
      style={{
        display: 'flex',
        flexDirection: 'column',
        gap: '3px',
        padding: '4px 0',
      }}
    >
      {gatein_type === GATEIN_TYPE.SELF ? (
        <Typography variant='body2' fontSize='0.8rem' noWrap>
          Self
        </Typography>
      ) : (
        <Typography
          component='a'
          color='primary'
          variant='body2'
          fontSize='0.8rem'
          noWrap
          href={`/app/registration/farmer/${farmer_id}`}
          style={{ textDecoration: 'none' }}
        >
          {farmer_name}
        </Typography>
      )}

      <Typography
        component='div'
        variant='caption'
        fontSize='0.7rem'
        color='textSecondary'
        sx={{
          overflow: 'hidden',
          textOverflow: 'ellipsis',
          display: '-webkit-box',
          WebkitLineClamp: 2,
          WebkitBoxOrient: 'vertical',
          lineHeight: 1.3,
        }}
      >
        {farmer_address}
      </Typography>
      <div style={{ marginTop: '2px' }}>{getPartnerStatusUI(rowData)}</div>
    </div>
  );
};

const ProductInfo = ({
  product,
  auction_date,
  token_suffix,
  inward_type,
  id,
  isLink,
}) => (
  <Typography component='div' variant='body2' fontSize='0.8rem' key={id}>
    {inward_type === INWARD_TYPE.CRATES ? (
      <>
        {product?.product_name} -- {product?.units} Unit |&nbsp;
        {isLink && (
          <Link
            href={`/app/regrade?
            auction_date=${auction_date}
            &token=${token_suffix}
            &product_id=${product?.product_id}
            &type=${INWARD_TYPE_PREFIX[inward_type]}`}
          >
            Regrade
          </Link>
        )}
      </>
    ) : (
      <>
        {product?.product_name} |&nbsp;
        {isLink && (
          <Link
            href={`/app/weighment?
            auction_date=${auction_date}
            &token=${token_suffix}
            &type=${INWARD_TYPE_PREFIX[inward_type]}`}
          >
            Trolley Weighment
          </Link>
        )}
      </>
    )}
  </Typography>
);

const ProductName = ({ product = {}, linkContent }) => (
  <>
    <Typography component='div' variant='body2' fontSize='0.8rem'>
      {product?.product_name}{' '}
      {linkContent && (
        <>
          <span>|&nbsp;</span> {linkContent}
        </>
      )}
    </Typography>
  </>
);

export const ProductCol = ({
  tab = '',
  auction_date = '',
  lots_data = [],
  inward_type = '',
  token_suffix = '',
}) => {
  const totalUnit = lots_data?.reduce((acc, cur) => {
    acc[cur.product_id]
      ? (acc[cur.product_id].units += cur?.units || 0)
      : (acc[cur.product_id] = { ...cur });
    return acc;
  }, {});
  const totalUnitData = Object.values(totalUnit);

  const totalWeight = lots_data?.reduce((acc, cur) => {
    acc[cur.product_id]
      ? acc[cur.product_id]
      : (acc[cur.product_id] = { ...cur });
    return acc;
  }, {});
  const lotsData = Object.values(totalWeight);

  return (
    <div
      style={{
        display: 'flex',
        flexDirection: 'column',
        gap: '3px',
        padding: '4px 0',
      }}
    >
      {tab === TOKEN_TABS.TO_BE_GRADED
        ? totalUnitData?.map((item, id) => (
            <ProductInfo
              key={id}
              product={item}
              auction_date={auction_date}
              token_suffix={token_suffix}
              inward_type={inward_type}
              isLink
            />
          ))
        : tab === TOKEN_TABS.AUCTION_READY
          ? lotsData.map((item, id) => (
              <ProductName
                key={id}
                product={item}
                linkContent={
                  <Link
                    href={`/app/auction-grading?auction_date=${auction_date}&token=${token_suffix}&type=${INWARD_TYPE_PREFIX[inward_type]}`}
                  >
                    Auction
                  </Link>
                }
              />
            ))
          : tab === TOKEN_TABS.CANCELLED
            ? totalUnitData.map((item, id) => (
                <ProductInfo
                  key={id}
                  product={item}
                  auction_date={auction_date}
                  token_suffix={token_suffix}
                  inward_type={inward_type}
                  isLink={false}
                />
              ))
            : lotsData?.map((item, id) => (
                <ProductName key={id} product={item} />
              ))}
    </div>
  );
};

export const UnitQuantityCol = ({
  tab = '',
  lots_data = [],
  auction_date = '',
  token_suffix = '',
  inward_type = '',
}) => {
  const totalWeight = lots_data.reduce((acc, cur) => {
    const { product_id, net_weight = 0, units = 0 } = cur;

    if (acc[product_id]) {
      acc[product_id].net_weight += net_weight;
      acc[product_id].units += units;
    } else {
      acc[product_id] = { ...cur };
    }
    return acc;
  }, {});

  const lotsData = Object.values(totalWeight);

  return (
    <div
      style={{
        display: 'flex',
        flexDirection: 'column',
        gap: '3px',
        padding: '4px 0',
      }}
    >
      {lotsData?.map((item, id) => {
        const {
          units = 0,
          net_weight = 0,
          weight = 0,
          is_delivered = false,
        } = item;

        const getContent = () => {
          if (tab === TOKEN_TABS.AUCTION_READY) {
            if (inward_type === INWARD_TYPE.CRATES) {
              return `${units} Units / ${toFixedNumber(net_weight)} Kg`;
            }
            return `${weight} Kg`;
          }
          if (tab === TOKEN_TABS.SOLD) {
            if (inward_type !== INWARD_TYPE.TRUCK) {
              return `${units} Units / ${toFixedNumber(net_weight) || 0} Kg`;
            }
            if (inward_type === INWARD_TYPE.TRUCK && is_delivered) {
              return `${toFixedNumber(net_weight) || 0} Kg`;
            }
            return (
              <>
                Tare weight pending |&nbsp;
                <Link
                  href={`/app/weighment?auction_date=${auction_date}&token=${token_suffix}&type=${INWARD_TYPE_PREFIX[inward_type]}`}
                >
                  Trolley Weighment
                </Link>
              </>
            );
          }
          return `${units} Units / ${toFixedNumber(net_weight)} Kg`;
        };

        return (
          <Typography
            key={id}
            component='div'
            variant='body2'
            fontSize='0.8rem'
            sx={{
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              whiteSpace: 'nowrap',
            }}
          >
            {getContent()}
          </Typography>
        );
      })}
    </div>
  );
};

export const TotalValuesCol = ({
  tab = '',
  openModal = () => {},
  openBreakDownModalFnc = () => {},
  handleFarmerBill = () => {},
  rowData = {},
}) => {
  const {
    lots_data = [],
    inward_type = '',
    net_amount = '',
    negative_bill_discount = 0,
    instant_pay_eligibility,
    instant_pay_reject_reason = '',
  } = rowData;
  const disabled =
    inward_type === INWARD_TYPE.TRUCK && !lots_data?.[0]?.is_delivered;
  return (
    <div
      style={{
        display: 'flex',
        flexDirection: 'column',
        gap: '4px',
        padding: '4px 0',
      }}
    >
      <div
        style={{
          display: 'flex',
          alignItems: 'center',
          gap: '8px',
        }}
      >
        <Typography
          component='div'
          variant='body2'
          fontSize='0.8rem'
          onClick={() => openBreakDownModalFnc(rowData)}
          style={{
            textDecoration: 'underline',
            cursor: 'pointer',
          }}
        >
          {tab !== TOKEN_TABS.SOLD ? (
            <>₹ {toFixedNumber(net_amount + negative_bill_discount, 2)}</>
          ) : (
            <>₹ {toFixedNumber(net_amount, 2)}</>
          )}
        </Typography>
        {tab !== TOKEN_TABS.SELF_SOLD && (
          <IconButton
            size='small'
            onClick={() => handleFarmerBill(rowData)}
            sx={{ padding: '4px', minWidth: '28px', height: '28px' }}
          >
            <VisibilityIcon fontSize='small' color='primary' />
          </IconButton>
        )}
      </div>

      <div style={{ display: 'flex', alignItems: 'center', gap: '4px' }}>
        {tab === TOKEN_TABS.SOLD ? (
          <>
            <Button
              color='primary'
              size='small'
              style={{
                paddingLeft: 0,
                textAlign: 'start',
                fontWeight: 'bold',
                fontSize: '0.7rem',
                minWidth: 'auto',
                height: '28px',
              }}
              onClick={() => openModal(rowData, net_amount)}
              disabled={disabled}
            >
              PAYMENT REQUEST
            </Button>
            {!instant_pay_eligibility && instant_pay_reject_reason?.length ? (
              <CustomTooltip
                arrow
                title={
                  <>
                    <Typography
                      color='error'
                      variant='body2'
                      fontSize='0.75rem'
                      style={{ textDecoration: 'underline' }}
                    >
                      Rejection Reason :
                    </Typography>
                    <Typography variant='body2' fontSize='0.75rem'>
                      {instant_pay_reject_reason}
                    </Typography>
                  </>
                }
              >
                <IconButton
                  size='small'
                  sx={{ padding: '4px', minWidth: '28px', height: '28px' }}
                >
                  <InfoRounded fontSize='small' color='primary' />
                </IconButton>
              </CustomTooltip>
            ) : null}
          </>
        ) : tab === TOKEN_TABS.PR_RAISED ? (
          rowData?.lots_data?.[0]?.purchase_order_id && (
            <Button
              color='primary'
              size='small'
              onClick={() => openModal(rowData)}
              style={{
                paddingLeft: 0,
                whiteSpace: 'nowrap',
                fontWeight: 'bold',
                fontSize: '0.7rem',
                minWidth: 'auto',
                height: '28px',
              }}
            >
              PR: {rowData?.lots_data?.[0]?.payment_request_id}
            </Button>
          )
        ) : (
          lots_data?.[0]?.purchase_order_id && (
            <Typography
              color='primary'
              variant='body2'
              fontSize='0.8rem'
              style={{ paddingLeft: 0 }}
            >
              <b>PR: {lots_data[0].payment_request_id}</b>&nbsp;
              {tab === TOKEN_TABS.PAID && <b>PAID</b>}
            </Typography>
          )
        )}
      </div>
    </div>
  );
};

export const ActionCol = ({
  farmerTokenId = '',
  setRecordArrival = () => {},
  recordArrival,
}) => {
  const [openModal, setOpenModal] = useState(false);
  const [loading, setLoading] = useState(false);
  const NotificationBar = useNotify();
  const isGateInRole = useRoleBasedAccess({ checkHasAccess: true });

  const handleSubmit = () => {
    setLoading(true);
    const reqBody = {
      farmer_token_id: farmerTokenId,
    };

    confirmRecordArrival(reqBody).then(({ responseData }) => {
      setOpenModal(false);
      NotificationBar(responseData);
      setRecordArrival(!recordArrival);
      setLoading(false);
    });
  };

  return (
    <>
      {isGateInRole && (
        <Typography
          color='primary'
          variant='body2'
          fontSize='0.8rem'
          onClick={() => {
            setOpenModal(true);
          }}
          style={{ textDecoration: 'underline' }}
        >
          Record Arrival
        </Typography>
      )}

      <Dialog open={openModal}>
        <Box
          display='flex'
          flexDirection='column'
          alignItems='center'
          justifyContent='center'
          style={{ padding: '1rem' }}
        >
          <DialogTitle variant='subtitle1' style={{ padding: '1rem 0' }}>
            <b>Do you want to Confirm Record Arrival ?</b>
          </DialogTitle>
          <DialogActions>
            <AppButton
              onClick={() => setOpenModal(!openModal)}
              disabled={loading}
              variant='contained'
              color='inherit'
            >
              Cancel
            </AppButton>
            <AppButton
              onClick={handleSubmit}
              variant='contained'
              color='primary'
              disabled={loading}
            >
              Confirm
            </AppButton>
          </DialogActions>
        </Box>
      </Dialog>
    </>
  );
};
