import React, { useState } from 'react';

import { Upload as UploadIcon, Edit as EditIcon } from '@mui/icons-material';
import { Button, Box, IconButton } from '@mui/material';

import ImageThumb from 'Components/ImageThumb';

import AttachmentUploadModal from './AttachmentUploadModal';

const AttachmentColumn = ({ rowData, onAttachmentSave, tab }) => {
  const [modalOpen, setModalOpen] = useState(false);
  const { uploads = [] } = rowData || {};
  const hasAttachments =
    uploads && Array.isArray(uploads) && uploads.length > 0;

  console.log('rowData', rowData);

  // Only show upload/edit functionality in SOLD tab
  const isSoldTab = tab === 'Sold';

  const handleOpenModal = () => {
    console.log('Opening attachment modal for token:', rowData?.token);
    setModalOpen(true);
  };

  const handleCloseModal = () => {
    setModalOpen(false);
  };

  const handleSave = async (
    tokenData,
    attachments,
    initiateInstantPayment = false
  ) => {
    // Call the parent function to handle the API call
    if (onAttachmentSave) {
      await onAttachmentSave(tokenData, attachments, initiateInstantPayment);
    }
  };

  if (!isSoldTab) {
    // For non-SOLD tabs, just show the attachment if it exists
    return hasAttachments ? (
      <ImageThumb file={uploads[0]?.url} url={uploads[0]?.url} />
    ) : null;
  }

  return (
    <Box display='flex' alignItems='center' gap={0.5}>
      {hasAttachments ? (
        <>
          <ImageThumb file={uploads[0]?.url} url={uploads[0]?.url} />
          <IconButton
            size='small'
            color='primary'
            onClick={handleOpenModal}
            title='Edit Attachment'
            sx={{ padding: '4px' }}
          >
            <EditIcon fontSize='small' />
          </IconButton>
        </>
      ) : (
        <Button
          variant='outlined'
          size='small'
          startIcon={<UploadIcon fontSize='small' />}
          onClick={handleOpenModal}
          sx={{
            fontSize: '0.7rem',
            padding: '2px 6px',
            minWidth: 'auto',
            height: '28px',
          }}
        >
          Upload
        </Button>
      )}

      {modalOpen && (
        <AttachmentUploadModal
          open={modalOpen}
          onClose={handleCloseModal}
          tokenData={rowData}
          onSave={handleSave}
          existingAttachments={
            Array.isArray(uploads) ? uploads.filter(Boolean) : []
          }
        />
      )}
    </Box>
  );
};

export default AttachmentColumn;
