import React, { useState, useEffect } from 'react';

import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Typography,
  Box,
} from '@mui/material';

import { useSiteValue } from 'App/SiteContext';
import { getSkuList } from 'Services/regrade';
import { toFixedNumber } from 'Utilities';
import { getCustomersName } from 'Utilities/constants';

const LotsPreviewTable = ({ tokenData, skuList }) => {
  if (!tokenData || !tokenData.lots_data || tokenData.lots_data.length === 0) {
    return (
      <Box p={2}>
        <Typography variant='body2' color='textSecondary'>
          No lots data available
        </Typography>
      </Box>
    );
  }

  // Helper functions defined before use
  const formatPrice = price => {
    if (!price) return '₹0';
    return `₹${toFixedNumber(price)}`;
  };

  const formatCustomer = (lot, customerId) => {

    if (customerId === -5) {
      const breachedCustomerName = 
        lot.dcl_breached_customer_name || 
        lot.dcl_breached_customer_short_code ||
        'DCL Breached Customer';
      const breachedCustomerShortCode = lot.dcl_breached_customer_short_code || '';
      
      if (breachedCustomerShortCode && breachedCustomerName !== breachedCustomerShortCode) {
        return `${breachedCustomerName} - ${breachedCustomerShortCode}`;
      }
      return breachedCustomerName;
    }


    if (customerId < 0) {
      const customerInfo = getCustomersName(lot) || {};
      const { name = '', customer_short_code = '' } = customerInfo;
      
      if (customer_short_code) {
        return customer_short_code;
      }
      if (name) {
        return name;
      }
      return 'N/A';
    }

    // Handle regular customers (positive IDs)
    if (lot.customer_short_code) {
      return lot.customer_short_code;
    }
    if (lot.customer_name) {
      return lot.customer_name;
    }
    return 'N/A';
  };

  const getSKUInfo = lot => {
    // First try to get SKU name from the lot data

    // If not available, try to find it in the SKU list using sku_id
    if (lot.sku_id && skuList.length > 0) {
      const sku = skuList.find(s => s.id === lot.sku_id);
      if (sku) {
        return sku.grade || sku.name;
      }
    }

    return 'N/A';
  };

  const getLotMarka = lot => {
    return lot.mandi_number.lot_marka || 'N/A';
  };

  // Group lots by mandi number, then by product, then by customer
  const groupedLots = tokenData.lots_data
    .sort((a, b) => {
      return a.sku_size_id - b.sku_size_id;
    })
    .reduce((acc, lot) => {
      const mandiNumber = lot.mandi_number?.mandi_number || 'N/A';
      const productName = lot.product_name || 'Unknown Product';
      const customerId = lot.customer_id;
      const customerKey = formatCustomer(lot, customerId);

      if (!acc[mandiNumber]) {
        acc[mandiNumber] = {};
      }
      if (!acc[mandiNumber][productName]) {
        acc[mandiNumber][productName] = {};
      }
      if (!acc[mandiNumber][productName][customerKey]) {
        acc[mandiNumber][productName][customerKey] = [];
      }

      acc[mandiNumber][productName][customerKey].push(lot);
      return acc;
    }, {});

  return (
    <Box>
      <TableContainer
        component={Paper}
        sx={{ maxHeight: 400, overflow: 'auto' }}
      >
        <Table stickyHeader size='small'>
          <TableHead>
            <TableRow>
              <TableCell
                sx={{
                  fontWeight: 'bold',
                  backgroundColor: '#f5f5f5',
                  textAlign: 'left',
                }}
              >
                Mandi No.
              </TableCell>
              <TableCell
                sx={{
                  fontWeight: 'bold',
                  backgroundColor: '#f5f5f5',
                  textAlign: 'left',
                }}
              >
                Product
              </TableCell>
              <TableCell
                sx={{
                  fontWeight: 'bold',
                  backgroundColor: '#f5f5f5',
                  textAlign: 'left',
                }}
              >
                Lot Marka
              </TableCell>
              <TableCell
                sx={{
                  fontWeight: 'bold',
                  backgroundColor: '#f5f5f5',
                  textAlign: 'left',
                }}
              >
                SKU
              </TableCell>
              <TableCell
                sx={{
                  fontWeight: 'bold',
                  backgroundColor: '#f5f5f5',
                  textAlign: 'left',
                }}
              >
                Units
              </TableCell>
              <TableCell
                sx={{
                  fontWeight: 'bold',
                  backgroundColor: '#f5f5f5',
                  textAlign: 'left',
                }}
              >
                Price/Unit
              </TableCell>
              <TableCell
                sx={{
                  fontWeight: 'bold',
                  backgroundColor: '#f5f5f5',
                  textAlign: 'left',
                }}
              >
                Customer
              </TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {Object.entries(groupedLots).map(([mandiNumber, productGroups]) => {
              // Calculate total rows for this mandi
              const mandiTotalRows = Object.values(productGroups).reduce(
                (total, customerGroups) =>
                  total +
                  Object.values(customerGroups).reduce(
                    (subTotal, lots) => subTotal + lots.length,
                    0
                  ),
                0
              );

              let mandiRowIndex = 0;

              return Object.entries(productGroups).map(
                ([productName, customerGroups]) => {
                  // Calculate total rows for this product
                  const productTotalRows = Object.values(customerGroups).reduce(
                    (total, lots) => total + lots.length,
                    0
                  );

                  let productRowIndex = 0;

                  return Object.entries(customerGroups).map(
                    ([customerKey, lots]) => {
                      const customerTotalRows = lots.length;

                      return lots.map((lot, lotIndex) => {
                        const isFirstMandiRow = mandiRowIndex === 0;
                        const isFirstProductRow = productRowIndex === 0;
                        const isFirstCustomerRow = lotIndex === 0;

                        mandiRowIndex++;
                        productRowIndex++;

                        const isLastRowInMandi =
                          mandiRowIndex === mandiTotalRows;
                        const isLastRowInProduct =
                          productRowIndex === productTotalRows;
                        const isLastRowInCustomer =
                          lotIndex === customerTotalRows - 1;

                        return (
                          <TableRow
                            key={`${mandiNumber}-${productName}-${customerKey}-${lotIndex}`}
                            sx={{
                              // Add border after last row of each group
                              ...((isLastRowInMandi ||
                                isLastRowInProduct ||
                                isLastRowInCustomer) && {
                                position: 'relative',
                                '&::after': {
                                  content: '""',
                                  position: 'absolute',
                                  bottom: 0,
                                  left: 0,
                                  right: 0,
                                  height: '2px',
                                  backgroundColor: '#f0f0f0',
                                  zIndex: 1,
                                },
                              }),
                            }}
                          >
                            {/* Mandi No. - Left aligned with rowspan */}
                            {isFirstMandiRow && (
                              <TableCell
                                rowSpan={mandiTotalRows}
                                sx={{
                                  textAlign: 'left',
                                  verticalAlign: 'middle',
                                }}
                              >
                                <Typography
                                  variant='body2'
                                  sx={{ fontWeight: 'bold' }}
                                >
                                  {mandiNumber}
                                </Typography>
                              </TableCell>
                            )}

                            {/* Product - Left aligned with rowspan */}
                            {isFirstProductRow && (
                              <TableCell
                                rowSpan={productTotalRows}
                                sx={{
                                  textAlign: 'left',
                                  verticalAlign: 'middle',
                                }}
                              >
                                <Typography
                                  variant='body2'
                                  sx={{ fontWeight: 'medium' }}
                                >
                                  {productName}
                                </Typography>
                              </TableCell>
                            )}

                            <TableCell sx={{ textAlign: 'left' }}>
                              <Typography variant='body2'>
                                {getLotMarka(lot)}
                              </Typography>
                            </TableCell>
                            <TableCell sx={{ textAlign: 'left' }}>
                              <Typography variant='body2'>
                                {getSKUInfo(lot)}
                              </Typography>
                            </TableCell>
                            <TableCell sx={{ textAlign: 'left' }}>
                              <Typography variant='body2'>
                                {lot.units || 0}
                              </Typography>
                            </TableCell>
                            <TableCell sx={{ textAlign: 'left' }}>
                              <Typography variant='body2'>
                                {formatPrice(
                                  lot.selling_price_per_unit ||
                                    lot.selling_price ||
                                    lot.price
                                )}
                              </Typography>
                            </TableCell>

                            {/* Customer - Left aligned with rowspan */}
                            {isFirstCustomerRow && (
                              <TableCell
                                rowSpan={customerTotalRows}
                                sx={{
                                  textAlign: 'left',
                                  verticalAlign: 'middle',
                                }}
                              >
                                <Typography
                                  variant='body2'
                                  sx={{
                                    fontWeight: 'medium',
                                    color:
                                      customerKey === 'New Customer'
                                        ? '#d32f2f'
                                        : 'inherit',
                                  }}
                                >
                                  {customerKey}
                                </Typography>
                              </TableCell>
                            )}
                          </TableRow>
                        );
                      });
                    }
                  );
                }
              );
            })}
          </TableBody>
        </Table>
      </TableContainer>
    </Box>
  );
};

export default LotsPreviewTable;
