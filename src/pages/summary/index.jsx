import React, { useEffect, useState } from 'react';

import { Grid, Typography } from '@mui/material';

import { useSiteValue } from 'App/SiteContext';
import ImageIcons from 'Components/AppIcons/ImageIcons';
import { getSummaryDetail } from 'Services/summary';
import { toFixedNumber } from 'Utilities';

import { InfoSection } from './styles.jsx';

const SummaryDashboard = () => {
  const [data, setData] = useState({});
  const { mandiId, auctionDate } = useSiteValue();

  useEffect(() => {
    if (mandiId) {
      getSummaryDetail({ mandi_id: mandiId, auction_date: auctionDate }).then(
        ({ responseData = {} }) => {
          setData(responseData);
        }
      );
    }
  }, [mandiId, auctionDate]);

  const {
    total_crates,
    total_trucks,
    total_farmers,
    total_kgs_sold,
    total_loaders,
    total_sales_value,
    total_crate_units,
    total_units_sold,
    total_full_boxes,
    total_half_boxes,
    total_boxes,
    rtf_units,
    enable_pack_acution,
  } = data || {};

  return (
    <Grid
      container
      style={{
        display: 'flex',
        alignItems: 'center',
        gap: '24px',
        padding: '0 24px',
      }}
    >
      <div>
        <Typography variant='h6' style={{ margin: '16px 0px' }}>
          <b>Summary</b>
        </Typography>
        <InfoSection>
          <div>
            <ImageIcons name='crates' width='40' />
            <Typography variant='h6'>Crates</Typography>
          </div>
          <Typography variant='h6'>
            <b>{total_crates}</b>
            {total_crate_units > 0 && (
              <Typography component='span' variant='body1'>
                ({total_crate_units} Units)
              </Typography>
            )}
          </Typography>
        </InfoSection>

        <InfoSection>
          <div>
            <ImageIcons name='truck' width='40' />
            <Typography variant='h6'>Trucks</Typography>
          </div>
          <Typography variant='h6'>
            <b>{total_trucks}</b>
          </Typography>
        </InfoSection>

        <InfoSection>
          <div>
            <Typography variant='h6'>Kgs sold</Typography>
          </div>
          <Typography variant='h6'>
            <b>{toFixedNumber(total_kgs_sold, 2)}</b>
          </Typography>
        </InfoSection>

        <InfoSection>
          <div>
            <Typography variant='h6'>Sales value</Typography>
          </div>
          <Typography variant='h6'>
            <b>₹{toFixedNumber(total_sales_value, 2)}</b>
          </Typography>
        </InfoSection>

        <InfoSection>
          <div>
            <Typography variant='h6'>Farmers</Typography>
          </div>
          <Typography variant='h6'>
            <b>{total_farmers}</b>
          </Typography>
        </InfoSection>

        <InfoSection>
          <div>
            <Typography variant='h6'>Loaders</Typography>
          </div>
          <Typography variant='h6'>
            <b>{total_loaders}</b>
          </Typography>
        </InfoSection>
      </div>

      {enable_pack_acution && (
        <div>
          <InfoSection>
            <div>
              <Typography variant='h6'>Total Units</Typography>
            </div>
            <Typography variant='h6'>
              <b>{total_units_sold}</b>
            </Typography>
          </InfoSection>

          <InfoSection>
            <div>
              <Typography variant='h6'> Full Boxes</Typography>
            </div>
            <Typography variant='h6'>
              <b>{total_full_boxes}</b>
            </Typography>
          </InfoSection>

          <InfoSection>
            <div>
              <Typography variant='h6'>Half Boxes</Typography>
            </div>
            <Typography variant='h6'>
              <b>{total_half_boxes}</b>
            </Typography>
          </InfoSection>

          <InfoSection>
            <div>
              <Typography variant='h6'>Boxes</Typography>
            </div>
            <Typography variant='h6'>
              <b>{total_boxes}</b>
            </Typography>
          </InfoSection>

          <InfoSection>
            <div>
              <Typography variant='h6'>Rtf units</Typography>
            </div>
            <Typography variant='h6'>
              <b>{rtf_units}</b>
            </Typography>
          </InfoSection>
        </div>
      )}
    </Grid>
  );
};

export default SummaryDashboard;
