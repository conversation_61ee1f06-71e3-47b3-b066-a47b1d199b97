import React, { useState, useEffect, useRef } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  Grid,
  IconButton,
  Typography,
  CircularProgress,
  Alert,
  Autocomplete,
  Box,
  useTheme,
  Paper,
  Divider,
  Chip,
  ToggleButtonGroup,
  ToggleButton,
} from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import ReceiptIcon from '@mui/icons-material/Receipt';
import LocalShippingIcon from '@mui/icons-material/LocalShipping';
import EventNoteIcon from '@mui/icons-material/EventNote';
import PersonIcon from '@mui/icons-material/Person';
import PrintIcon from '@mui/icons-material/Print';
import AddIcon from '@mui/icons-material/Add';
import SearchIcon from '@mui/icons-material/Search';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { format } from 'date-fns';
import { fetchInventoryAutofill, fetchDeliveryChallan } from '../../services/inventoryService';
import { useSiteValue } from '../../app/SiteContext';
import PrintDispatchChallan from '../../components/PrintSlips/PrintDispatchChallan/PrintDispatchChallan';
import DispatchChallanViewer from './DispatchChallanViewer';
import { useReactToPrint } from 'react-to-print';

const DispatchChallanModal = ({ open, onClose, onSubmit, isLoading, error, selectedCustomer: propSelectedCustomer, selectedMandiNumber, onClearError }) => {
  const theme = useTheme();
  const printRef = useRef();
  
  // Mode toggle state
  const [mode, setMode] = useState('create'); // 'create' or 'print'
  
  // Create mode state
  const [transactionDate, setTransactionDate] = useState(new Date());
  const [customerId, setCustomerId] = useState('');
  const [vehicleNumber, setVehicleNumber] = useState('');
  
  // New additional fields for create mode
  const [toLocation, setToLocation] = useState('');
  const [totalCases, setTotalCases] = useState('');
  const [freightPerBox, setFreightPerBox] = useState('');
  const [totalFreight, setTotalFreight] = useState('');
  const [advance, setAdvance] = useState('');
  const [driverName, setDriverName] = useState('');
  const [driverContact, setDriverContact] = useState('');
  const [vehicleOwnerName, setVehicleOwnerName] = useState('');
  const [vehicleType, setVehicleType] = useState('');
  
  // Print mode state
  const [challanData, setChallanData] = useState(null);
  const [challanNumber, setChallanNumber] = useState('');
  const [challanDate, setChallanDate] = useState(new Date());
  const [fetchingChallan, setFetchingChallan] = useState(false);
  const [challanViewerOpen, setChallanViewerOpen] = useState(false);
  const [printFormErrors, setPrintFormErrors] = useState({
    challanNumber: '',
    challanDate: '',
    general: ''
  });
  
  // Local error state to handle API errors separately for each mode
  const [localError, setLocalError] = useState('');
  
  const [formErrors, setFormErrors] = useState({
    transactionDate: '',
    customerId: '',
    vehicleNumber: '',
    toLocation: '',
    totalCases: '',
    freightPerBox: '',
    totalFreight: '',
    driverName: '',
    driverContact: '',
    vehicleType: '',
  });
  
  // Autofill data
  const [autofillData, setAutofillData] = useState([]);
  const [customerOptions, setCustomerOptions] = useState([]);
  const [vehicleOptions, setVehicleOptions] = useState([]);
  const [autofillLoading, setAutofillLoading] = useState(false);
  const [selectedCustomer, setSelectedCustomer] = useState(null);
  
  const siteContext = useSiteValue();
  const mandiId = siteContext?.mandiId;

  useEffect(() => {
    if (open && mandiId) {
      fetchAutofillData();
    }
  }, [open, transactionDate, mandiId]);
  

  useEffect(() => {
    if (open) {
      setMode('create');
      

      setChallanData(null);
      setChallanNumber('');
      setChallanDate(new Date());
      setChallanViewerOpen(false);
      setPrintFormErrors({
        challanNumber: '',
        challanDate: '',
        general: ''
      });
      
      setTransactionDate(new Date());
      
      if (propSelectedCustomer) {
        setCustomerId(propSelectedCustomer.id.toString());
        setSelectedCustomer(propSelectedCustomer);
      }
      
      // if (selectedMandiNumber) {
      //   setMandiNumberId(selectedMandiNumber.id.toString());
      // }
      
      if (!propSelectedCustomer && !selectedMandiNumber) {
        fetchAutofillData();
      }
    }
  }, [open, propSelectedCustomer, selectedMandiNumber]);
  
  useEffect(() => {
    if (open && mandiId && customerId) {
      fetchAutofillData(customerId);
    }
  }, [customerId]);
  
  useEffect(() => {
    if (autofillData.length > 0) {
      const uniqueCustomers = [];
      const customerMap = new Map();
      
      autofillData.forEach(item => {
        if (!customerMap.has(item.customer_id)) {
          customerMap.set(item.customer_id, true);
          uniqueCustomers.push({
            id: item.customer_id,
            label: `${item.customer_short_code} (${item.customer_id})`,
            name: item.customer_name,
            shortCode: item.customer_short_code
          });
        }
      });
      
      setCustomerOptions(uniqueCustomers);
      
      // Create vehicle options based on selected customer or all vehicles
      const filteredVehicles = customerId 
        ? autofillData.filter(item => item.customer_id.toString() === customerId.toString())
        : autofillData;
      
      const uniqueVehicles = [];
      const vehicleMap = new Map();
      
      filteredVehicles.forEach(item => {
        if (item.vehicle_number && !vehicleMap.has(item.vehicle_number)) {
          vehicleMap.set(item.vehicle_number, true);
          uniqueVehicles.push({
            value: item.vehicle_number,
            label: item.vehicle_number
          });
        }
      });
      
      setVehicleOptions(uniqueVehicles);
    }
  }, [autofillData, customerId]);
  
  // Fetch autofill data from API
  const fetchAutofillData = async (customerIdParam = null) => {
    if (!mandiId) {
      console.warn('No mandi ID available for autofill');
      return;
    }
    
    setAutofillLoading(true);
    try {
      // Create a new date object and set to start of day
      const dateObj = new Date(transactionDate);
      dateObj.setHours(0, 0, 0, 0);
      
      const params = {
        mandi_id: mandiId,
        transaction_date: dateObj.getTime() // Send as timestamp of start of day
      };
      
      if (customerIdParam) {
        params.customer_id = customerIdParam;
      }
      
      console.log('Fetching autofill data with params:', params);
      const data = await fetchInventoryAutofill(params);
      console.log('Autofill data received:', data);
      setAutofillData(data || []);
    } catch (error) {
      console.error('Error fetching autofill data:', error);
    } finally {
      setAutofillLoading(false);
    }
  };

  // Function to validate Indian phone number
  const validateIndianPhoneNumber = (phone) => {
    const phoneRegex = /^[6-9]\d{9}$/;
    return phoneRegex.test(phone);
  };

  const validateForm = () => {
    // Only validate if we're in create mode
    if (mode !== 'create') return true;
    
    const errors = {
      transactionDate: !transactionDate ? 'Transaction date is required' : '',
      customerId: !customerId ? 'Customer ID is required' : '',
      vehicleNumber: !vehicleNumber ? 'Vehicle number is required' : '',
      toLocation: !toLocation ? 'Destination location is required' : '',
      driverName: !driverName ? 'Driver name is required' : '',
      driverContact: !driverContact ? 'Driver contact is required' : 
                    !validateIndianPhoneNumber(driverContact) ? 'Enter a valid 10-digit mobile number' : '',
      vehicleType: !vehicleType ? 'Vehicle type is required' : '',
      totalCases: !totalCases ? 'Total cases is required' : 
                 isNaN(totalCases) ? 'Total cases must be a number' : '',
      freightPerBox: !freightPerBox ? 'Freight per box is required' : 
                    isNaN(freightPerBox) ? 'Freight per box must be a number' : '',
      totalFreight: !totalFreight ? 'Total freight is required' : 
                   isNaN(totalFreight) ? 'Total freight must be a number' : '',
    };
    setFormErrors(errors);
    return !Object.values(errors).some(error => error);
  };
  
  const validatePrintForm = () => {
    const errors = {
      challanDate: !challanDate ? 'Challan Date is required' : '',
    };
    setPrintFormErrors(errors);
    return !Object.values(errors).some(error => error);
  };

  // Fetch challan data for print mode
  const fetchChallanData = async () => {
    // Only validate print form fields, not create form fields
    if (!validatePrintForm()) return;
    
    try {
      setFetchingChallan(true);
      setChallanData(null);
      setPrintFormErrors(prev => ({ ...prev, general: '' }));
      
      // Clear any create mode errors that might be showing
      setFormErrors({
        transactionDate: '',
        customerId: '',
        vehicleNumber: '',
        toLocation: '',
        totalCases: '',
        freightPerBox: '',
        totalFreight: '',
        driverName: '',
        driverContact: '',
        vehicleType: '',
      });
      
      const formattedDate = format(challanDate, 'yyyy-MM-dd');

      const dateObj = new Date(formattedDate);
      dateObj.setHours(0, 0, 0, 0);
      
      const params = {
        challan_date: dateObj.getTime(),
        mandi_id: parseInt(mandiId),
      };
      
      // Only add challan_number to params if it's provided
      if (challanNumber) {
        params.challan_number = challanNumber;
      }
      
      console.log('Fetching challan data with params:', params);
      const data = await fetchDeliveryChallan(params);
      console.log('Challan data received:', data);
      
      if (data && Object.keys(data).length > 0) {
        setChallanData(data);
        // Open the challan viewer with the fetched data
        setChallanViewerOpen(true);
      } else {
        // Handle no data found
        setChallanData(null);
        throw new Error('No challan data found for the given parameters');
      }
    } catch (error) {
      console.error('Error fetching challan data:', error);
      // Show error message
      setPrintFormErrors(prev => ({
        ...prev,
        general: error.message || 'Failed to fetch challan data. Please try again.'
      }));
    } finally {
      setFetchingChallan(false);
    }
  };
  
  // Handle print functionality
  const handlePrint = useReactToPrint({
    content: () => printRef.current,
    documentTitle: `Dispatch_Challan_${challanNumber}`,
    onBeforeGetContent: () => {
      return new Promise((resolve) => {
        resolve();
      });
    },
    onAfterPrint: () => {
      console.log('Print completed');
    },
  });
  
  const handleSubmit = () => {
    if (mode === 'create') {
      if (validateForm()) {
        const dateObj = new Date(transactionDate);
        dateObj.setHours(0, 0, 0, 0);
        
        onSubmit({
          transaction_date: dateObj.getTime(),
          customer_id: parseInt(customerId, 10), // Convert to integer
          vehicle_number: vehicleNumber,
          mandi_id: mandiId,
          to_location: toLocation,
          total_cases: parseFloat(totalCases),
          freight_per_box: parseFloat(freightPerBox),
          total_freight: parseFloat(totalFreight),
          advance: advance ? parseFloat(advance) : 0,
          driver_name: driverName,
          driver_contact: driverContact,
          vehicle_owner_name: vehicleOwnerName,
          vehicle_type: vehicleType,
        });
        
        // Reset form state after successful submit
        resetForm();
      }
    } else if (mode === 'print') {
      if (challanData) {
        // If we already have challan data, print it
        handlePrint();
      } else {
        // Otherwise fetch the challan data
        fetchChallanData();
      }
    }
  };
  
  // Function to reset all form fields
  const resetForm = () => {
    // Reset create mode fields
    setTransactionDate(new Date());
    setCustomerId('');
    setVehicleNumber('');
    setToLocation('');
    setTotalCases('');
    setFreightPerBox('');
    setTotalFreight('');
    setAdvance('');
    setDriverName('');
    setDriverContact('');
    setVehicleOwnerName('');
    setVehicleType('');
    setSelectedCustomer(null);
    setFormErrors({
      transactionDate: '',
      customerId: '',
      vehicleNumber: '',
      toLocation: '',
      totalCases: '',
      freightPerBox: '',
      totalFreight: '',
      driverName: '',
      driverContact: '',
      vehicleType: '',
    });
    
    // Reset print mode fields
    setChallanNumber('');
    setChallanDate(new Date());
    setChallanData(null);
    setPrintFormErrors({
      challanNumber: '',
      challanDate: '',
      general: ''
    });
  };
  
  // Handle mode change
  const handleModeChange = (event, newMode) => {
    if (newMode !== null) {
      setMode(newMode);
      // Reset form data when switching modes
      resetForm();
      // Clear any challan data
      setChallanData(null);
      // Clear any API errors when switching modes
      if (onClearError && typeof onClearError === 'function') {
        onClearError();
      }
    }
  };
  
  const handleCustomerChange = (event, newValue) => {
    if (newValue) {
      setCustomerId(newValue.id.toString());
      setSelectedCustomer(newValue);
      setFormErrors(prev => ({ ...prev, customerId: '' }));
    } else {
      setCustomerId('');
      setSelectedCustomer(null);
    }
  };
  
  const handleVehicleChange = (event, newValue) => {
    if (newValue) {
      setVehicleNumber(newValue.value);
      setFormErrors(prev => ({ ...prev, vehicleNumber: '' }));
    } else {
      setVehicleNumber('');
    }
  };

  const handleClose = () => {
    resetForm();
    setMode('create'); // Reset to create mode when closing
    setAutofillData([]);
    setAutofillLoading(false);
    onClose();
  };
  
  // Form errors are already reset in resetForm()

  return (
    <React.Fragment>
      <Dialog 
        open={open} 
        onClose={handleClose} 
        maxWidth="md" 
        fullWidth
        PaperProps={{
          sx: {
            borderRadius: '12px',
            boxShadow: '0 8px 32px rgba(0,0,0,0.15)',
            minHeight: '450px',
            overflow: 'hidden'
          }
        }}
      >
        <DialogTitle sx={{ 
          backgroundColor: theme.palette.primary.main, 
          color: 'white',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          p: 2
        }}>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            {mode === 'create' ? <LocalShippingIcon sx={{ mr: 1 }} /> : <PrintIcon sx={{ mr: 1 }} />}
            {mode === 'create' ? 'Create Dispatch Challan' : 'Print Existing Challan'}
          </Box>
          <IconButton
            aria-label="close"
            onClick={handleClose}
            sx={{ color: 'white' }}
          >
            <CloseIcon />
          </IconButton>
      </DialogTitle>

      <DialogContent sx={{ p: 3 }}>
        {/* Mode Toggle */}
        <Box sx={{ mb: 3, display: 'flex', justifyContent: 'center' }}>
          <ToggleButtonGroup
            value={mode}
            exclusive
            onChange={handleModeChange}
            aria-label="challan mode"
            sx={{ 
              backgroundColor: '#f8f9fa',
              '& .MuiToggleButtonGroup-grouped': {
                border: '1px solid rgba(0,0,0,0.12)',
                '&:not(:first-of-type)': {
                  borderLeft: '1px solid rgba(0,0,0,0.12)',
                },
              },
            }}
          >
            <ToggleButton 
              value="create" 
              aria-label="create new challan"
              sx={{ 
                px: 3, 
                py: 1,
                textTransform: 'none',
                '&.Mui-selected': {
                  backgroundColor: theme.palette.primary.main,
                  color: 'white',
                  '&:hover': {
                    backgroundColor: theme.palette.primary.dark,
                  }
                }
              }}
            >
              <AddIcon sx={{ mr: 1, fontSize: '1rem' }} />
              Create New Challan
            </ToggleButton>
            <ToggleButton 
              value="print" 
              aria-label="print existing challan"
              sx={{ 
                px: 3, 
                py: 1,
                textTransform: 'none',
                '&.Mui-selected': {
                  backgroundColor: theme.palette.primary.main,
                  color: 'white',
                  '&:hover': {
                    backgroundColor: theme.palette.primary.dark,
                  }
                }
              }}
            >
              <PrintIcon sx={{ mr: 1, fontSize: '1rem' }} />
              Print Existing Challan
            </ToggleButton>
          </ToggleButtonGroup>
        </Box>
        
        {/* Print Mode Form */}
        {mode === 'print' && (
          <Paper elevation={0} sx={{ 
            p: 3, 
            backgroundColor: '#f8f9fa',
            borderRadius: '8px',
            border: '1px solid rgba(0,0,0,0.08)',
            mb: 3
          }}>
            <Box sx={{ 
              display: 'flex', 
              alignItems: 'center', 
              mb: 3,
              pb: 2,
              borderBottom: `1px solid ${theme.palette.divider}`
            }}>
              <PrintIcon sx={{ 
                mr: 2, 
                color: theme.palette.primary.main,
                fontSize: '1.5rem' 
              }} />
              <Typography variant="h6" sx={{ 
                fontWeight: 500,
                color: theme.palette.text.primary,
              }}>
                Search Challan
              </Typography>
            </Box>
            <Grid container spacing={2}>
              {/* Customer ID field has been removed */}
              
              {/* Challan Number */}
              <Grid item xs={12} md={6}>
                <Box sx={{ 
                  display: 'flex', 
                  flexDirection: 'column',
                  mb: { xs: 1, md: 2 }
                }}>
                  <Typography variant="caption" sx={{ 
                    mb: 0.5, 
                    color: theme.palette.text.secondary,
                    display: 'flex',
                    alignItems: 'center'
                  }}>
                    <ReceiptIcon sx={{ fontSize: '0.9rem', mr: 0.5 }} />
                    Challan Number
                  </Typography>
                  <TextField
                    value={challanNumber}
                    onChange={(e) => {
                      setChallanNumber(e.target.value);
                      setPrintFormErrors(prev => ({ ...prev, challanNumber: '' }));
                    }}
                    error={!!printFormErrors.challanNumber}
                    helperText={printFormErrors.challanNumber}
                    placeholder="Enter Challan Number"
                    fullWidth
                    variant="outlined"
                    InputProps={{
                      sx: { height: '48px' }
                    }}
                    sx={{
                      '& .MuiOutlinedInput-root': {
                        backgroundColor: 'white',
                        borderRadius: '8px',
                        '&:hover .MuiOutlinedInput-notchedOutline': {
                          borderColor: theme.palette.primary.main,
                          borderWidth: '1px'
                        },
                        '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                          borderColor: theme.palette.primary.main,
                          borderWidth: '2px'
                        }
                      }
                    }}
                  />
                </Box>
              </Grid>
              
              {/* Challan Date */}
              <Grid item xs={12} md={6}>
                <Box sx={{ 
                  display: 'flex', 
                  flexDirection: 'column',
                  mb: { xs: 1, md: 2 }
                }}>
                  <Typography variant="caption" sx={{ 
                    mb: 0.5, 
                    color: theme.palette.text.secondary,
                    display: 'flex',
                    alignItems: 'center'
                  }}>
                    <EventNoteIcon sx={{ fontSize: '0.9rem', mr: 0.5 }} />
                    Challan Date*
                  </Typography>
                  <LocalizationProvider dateAdapter={AdapterDateFns}>
                    <DatePicker
                      value={challanDate}
                      onChange={(newDate) => {
                        setChallanDate(newDate);
                        setPrintFormErrors(prev => ({ ...prev, challanDate: '' }));
                      }}
                      slotProps={{
                        textField: {
                          fullWidth: true,
                          variant: 'outlined',
                          error: !!printFormErrors.challanDate,
                          helperText: printFormErrors.challanDate,
                          InputProps: {
                            sx: { height: '48px' }
                          },
                          sx: {
                            '& .MuiOutlinedInput-root': {
                              backgroundColor: 'white',
                              borderRadius: '8px',
                              '&:hover .MuiOutlinedInput-notchedOutline': {
                                borderColor: theme.palette.primary.main,
                                borderWidth: '1px'
                              },
                              '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                                borderColor: theme.palette.primary.main,
                                borderWidth: '2px'
                              }
                            }
                          }
                        }
                      }}
                    />
                  </LocalizationProvider>
                </Box>
              </Grid>
            </Grid>
            
            {/* Print Form Error Message */}
            {printFormErrors.general && (
              <Alert 
                severity="error" 
                variant="filled"
                sx={{ 
                  mt: 2,
                  borderRadius: '8px',
                  '& .MuiAlert-icon': {
                    alignItems: 'center'
                  }
                }}
              >
                {printFormErrors.general}
              </Alert>
            )}
            
            {/* Print Preview */}
            {challanData && (
              <Box sx={{ mt: 3, display: 'none' }}>
                <div ref={printRef}>
                  <PrintDispatchChallan data={challanData} />
                </div>
              </Box>
            )}
            
            {/* Success Message */}
            {challanData && (
              <Alert 
                severity="success" 
                variant="filled"
                sx={{ 
                  mt: 2,
                  borderRadius: '8px',
                  '& .MuiAlert-icon': {
                    alignItems: 'center'
                  }
                }}
              >
                Challan found! Click the "Print Challan" button to print.
              </Alert>
            )}
          </Paper>
        )}
        
        {/* Create Mode Form */}
        {mode === 'create' && (
          <Paper elevation={0} sx={{ 
            p: 3, 
            backgroundColor: '#f8f9fa',
            borderRadius: '8px',
            border: '1px solid rgba(0,0,0,0.08)',
            mb: 3
          }}>
            <Box sx={{ 
              display: 'flex', 
              alignItems: 'center', 
              mb: 3,
              pb: 2,
              borderBottom: `1px solid ${theme.palette.divider}`
            }}>
              <ReceiptIcon sx={{ 
                mr: 2, 
                color: theme.palette.primary.main,
                fontSize: '1.5rem' 
              }} />
              <Typography variant="h6" sx={{ 
                fontWeight: 500,
                color: theme.palette.text.primary,
              }}>
                Dispatch Details
              </Typography>
            </Box>
        

          <Grid container spacing={3}>
            <Grid item xs={12} md={4} lg={4}>
              <Box sx={{ 
                display: 'flex', 
                flexDirection: 'column',
                mb: { xs: 1, md: 2 }
              }}>
                <Typography variant="caption" sx={{ 
                  mb: 0.5, 
                  color: theme.palette.text.secondary,
                  display: 'flex',
                  alignItems: 'center'
                }}>
                  <EventNoteIcon sx={{ fontSize: '0.9rem', mr: 0.5 }} />
                  Transaction Date*
                </Typography>
                <LocalizationProvider dateAdapter={AdapterDateFns}>
                  <DatePicker
                    value={transactionDate}
                    onChange={(newValue) => {
                      setTransactionDate(newValue);
                      if (newValue) {
                        setFormErrors(prev => ({ ...prev, transactionDate: '' }));
                      }
                    }}
                    slotProps={{
                      textField: {
                        fullWidth: true,
                        variant: 'outlined',
                        error: !!formErrors.transactionDate,
                        helperText: formErrors.transactionDate,
                        placeholder: 'Select date',
                        size: 'medium',
                        sx: { 
                          '& .MuiInputBase-root': { 
                            height: '48px',
                            borderRadius: '8px',
                          },
                          '& .MuiOutlinedInput-root': {
                            backgroundColor: 'white',
                            '&:hover .MuiOutlinedInput-notchedOutline': {
                              borderColor: theme.palette.primary.main,
                              borderWidth: '1px'
                            },
                            '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                              borderColor: theme.palette.primary.main,
                              borderWidth: '2px'
                            }
                          }
                        }
                      }
                    }}
                  />
                </LocalizationProvider>
              </Box>
            </Grid>

            <Grid item xs={12} md={4} lg={4}>
              <Box sx={{ 
                display: 'flex', 
                flexDirection: 'column',
                minWidth: '150px',
                mb: { xs: 1, md: 2 }
              }}>
                <Typography variant="caption" sx={{ 
                  mb: 0.5, 
                  color: theme.palette.text.secondary,
                  display: 'flex',
                  alignItems: 'center'
                }}>
                  <PersonIcon sx={{ fontSize: '0.9rem', mr: 0.5 }} />
                  Customer*
                </Typography>
                <Autocomplete
                  options={customerOptions}
                  getOptionLabel={(option) => option.label || ''}
                  value={selectedCustomer}
                  onChange={handleCustomerChange}
                  isOptionEqualToValue={(option, value) => option.id === value.id}
                  renderInput={(params) => (
                    <TextField
                      {...params}
                      error={!!formErrors.customerId}
                      helperText={formErrors.customerId}
                      placeholder="Search and select a customer"
                      fullWidth
                      variant="outlined"
                      InputProps={{
                        ...params.InputProps,
                        sx: { height: '56px' }
                      }}
                      sx={{
                        '& .MuiOutlinedInput-root': {
                          backgroundColor: 'white',
                          borderRadius: '8px',
                          '&:hover .MuiOutlinedInput-notchedOutline': {
                            borderColor: theme.palette.primary.main,
                            borderWidth: '1px'
                          },
                          '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                            borderColor: theme.palette.primary.main,
                            borderWidth: '2px'
                          }
                        }
                      }}
                    />
                  )}
                  renderOption={(props, option) => (
                    <li {...props}>
                      <Box sx={{ py: 0.5 }}>
                        <Typography variant="body2" sx={{ fontWeight: 500 }}>
                          {option.label}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          {option.name}
                        </Typography>
                      </Box>
                    </li>
                  )}
                  loading={autofillLoading}
                  loadingText={
                    <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', py: 1 }}>
                      <CircularProgress size={16} sx={{ mr: 1 }} />
                      <Typography variant="body2">Loading customers...</Typography>
                    </Box>
                  }
                  noOptionsText="No customers found"
                />
              </Box>
            </Grid>

            <Grid item xs={12} md={4} lg={4}>
              <Box sx={{ 
                display: 'flex', 
                flexDirection: 'column',
                minWidth: '150px',
                mb: { xs: 1, md: 2 }
              }}>
                <Typography variant="caption" sx={{ 
                  mb: 0.5, 
                  color: theme.palette.text.secondary,
                  display: 'flex',
                  alignItems: 'center'
                }}>
                  <LocalShippingIcon sx={{ fontSize: '0.9rem', mr: 0.5 }} />
                  Vehicle Number*
                </Typography>
                <Autocomplete
                  options={vehicleOptions}
                  getOptionLabel={(option) => option.label || ''}
                  value={vehicleOptions.find(option => option.value === vehicleNumber) || null}
                  onChange={handleVehicleChange}
                  isOptionEqualToValue={(option, value) => option.value === value.value}
                  renderInput={(params) => (
                    <TextField
                      {...params}
                      error={!!formErrors.vehicleNumber}
                      helperText={formErrors.vehicleNumber}
                      placeholder="Select or enter a vehicle number"
                      fullWidth
                      variant="outlined"
                      InputProps={{
                        ...params.InputProps,
                        sx: { height: '56px' }
                      }}
                      sx={{
                        '& .MuiOutlinedInput-root': {
                          backgroundColor: 'white',
                          borderRadius: '8px',
                          '&:hover .MuiOutlinedInput-notchedOutline': {
                            borderColor: theme.palette.primary.main,
                            borderWidth: '1px'
                          },
                          '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                            borderColor: theme.palette.primary.main,
                            borderWidth: '2px'
                          }
                        }
                      }}
                    />
                  )}
                  freeSolo
                  loading={autofillLoading}
                  loadingText={
                    <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', py: 1 }}>
                      <CircularProgress size={16} sx={{ mr: 1 }} />
                      <Typography variant="body2">Loading vehicles...</Typography>
                    </Box>
                  }
                  noOptionsText="No vehicles found"
                  onInputChange={(event, newInputValue) => {
                    if (newInputValue) {
                      setVehicleNumber(newInputValue);
                      setFormErrors(prev => ({ ...prev, vehicleNumber: '' }));
                    } else {
                      setVehicleNumber('');
                    }
                  }}
                />
              </Box>
            </Grid>
            
            {/* Location Details */}
            <Grid item xs={12} md={4} lg={4}>
              <Box sx={{ 
                display: 'flex', 
                flexDirection: 'column',
                minWidth: '150px',
                mb: { xs: 1, md: 2 }
              }}>
                <Typography variant="caption" sx={{ 
                  mb: 0.5, 
                  color: theme.palette.text.secondary,
                  display: 'flex',
                  alignItems: 'center'
                }}>
                  <EventNoteIcon sx={{ fontSize: '0.9rem', mr: 0.5 }} />
                  Destination Location*
                </Typography>
                <TextField
                  value={toLocation}
                  onChange={(e) => {
                    setToLocation(e.target.value);
                    setFormErrors(prev => ({ ...prev, toLocation: '' }));
                  }}
                  error={!!formErrors.toLocation}
                  helperText={formErrors.toLocation}
                  placeholder="Enter destination location"
                  fullWidth
                  variant="outlined"
                  InputProps={{
                    sx: { height: '48px' }
                  }}
                  sx={{
                    '& .MuiOutlinedInput-root': {
                      backgroundColor: 'white',
                      borderRadius: '8px',
                      '&:hover .MuiOutlinedInput-notchedOutline': {
                        borderColor: theme.palette.primary.main,
                        borderWidth: '1px'
                      },
                      '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                        borderColor: theme.palette.primary.main,
                        borderWidth: '2px'
                      }
                    }
                  }}
                />
              </Box>
            </Grid>

            {/* Vehicle Type */}
            <Grid item xs={12} md={4} lg={4}>
              <Box sx={{ 
                display: 'flex', 
                flexDirection: 'column',
                minWidth: '150px',
                mb: { xs: 1, md: 2 }
              }}>
                <Typography variant="caption" sx={{ 
                  mb: 0.5, 
                  color: theme.palette.text.secondary,
                  display: 'flex',
                  alignItems: 'center'
                }}>
                  <LocalShippingIcon sx={{ fontSize: '0.9rem', mr: 0.5 }} />
                  Vehicle Type*
                </Typography>
                <TextField
                  value={vehicleType}
                  onChange={(e) => {
                    setVehicleType(e.target.value);
                    setFormErrors(prev => ({ ...prev, vehicleType: '' }));
                  }}
                  error={!!formErrors.vehicleType}
                  helperText={formErrors.vehicleType}
                  placeholder="E.g., Truck, Van, etc."
                  fullWidth
                  variant="outlined"
                  InputProps={{
                    sx: { height: '48px' }
                  }}
                  sx={{
                    '& .MuiOutlinedInput-root': {
                      backgroundColor: 'white',
                      borderRadius: '8px',
                      '&:hover .MuiOutlinedInput-notchedOutline': {
                        borderColor: theme.palette.primary.main,
                        borderWidth: '1px'
                      },
                      '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                        borderColor: theme.palette.primary.main,
                        borderWidth: '2px'
                      }
                    }
                  }}
                />
              </Box>
            </Grid>
          </Grid>
          
          <Typography variant="h6" sx={{ 
            mt: 3, 
            mb: 2,
            fontWeight: 500,
            color: theme.palette.text.primary,
            fontSize: '1rem'
          }}>
            Driver & Freight Details
          </Typography>

          <Grid container spacing={3}>
            {/* Driver Name */}
            <Grid item xs={12} md={4} lg={4}>
              <Box sx={{ 
                display: 'flex', 
                flexDirection: 'column',
                mb: { xs: 1, md: 2 }
              }}>
                <Typography variant="caption" sx={{ 
                  mb: 0.5, 
                  color: theme.palette.text.secondary,
                  display: 'flex',
                  alignItems: 'center'
                }}>
                  <PersonIcon sx={{ fontSize: '0.9rem', mr: 0.5 }} />
                  Driver Name*
                </Typography>
                <TextField
                  value={driverName}
                  onChange={(e) => {
                    setDriverName(e.target.value);
                    setFormErrors(prev => ({ ...prev, driverName: '' }));
                  }}
                  error={!!formErrors.driverName}
                  helperText={formErrors.driverName}
                  placeholder="Enter driver name"
                  fullWidth
                  variant="outlined"
                  InputProps={{
                    sx: { height: '48px' }
                  }}
                  sx={{
                    '& .MuiOutlinedInput-root': {
                      backgroundColor: 'white',
                      borderRadius: '8px',
                      '&:hover .MuiOutlinedInput-notchedOutline': {
                        borderColor: theme.palette.primary.main,
                        borderWidth: '1px'
                      },
                      '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                        borderColor: theme.palette.primary.main,
                        borderWidth: '2px'
                      }
                    }
                  }}
                />
              </Box>
            </Grid>

            {/* Driver Contact */}
            <Grid item xs={12} md={4} lg={4}>
              <Box sx={{ 
                display: 'flex', 
                flexDirection: 'column',
                mb: { xs: 1, md: 2 }
              }}>
                <Typography variant="caption" sx={{ 
                  mb: 0.5, 
                  color: theme.palette.text.secondary,
                  display: 'flex',
                  alignItems: 'center'
                }}>
                  <PersonIcon sx={{ fontSize: '0.9rem', mr: 0.5 }} />
                  Driver Contact*
                </Typography>
                <TextField
                  value={driverContact}
                  onChange={(e) => {
                    setDriverContact(e.target.value);
                    setFormErrors(prev => ({ ...prev, driverContact: '' }));
                  }}
                  error={!!formErrors.driverContact}
                  helperText={formErrors.driverContact}
                  placeholder="Enter driver contact number"
                  fullWidth
                  variant="outlined"
                  InputProps={{
                    sx: { height: '48px' }
                  }}
                  sx={{
                    '& .MuiOutlinedInput-root': {
                      backgroundColor: 'white',
                      borderRadius: '8px',
                      '&:hover .MuiOutlinedInput-notchedOutline': {
                        borderColor: theme.palette.primary.main,
                        borderWidth: '1px'
                      },
                      '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                        borderColor: theme.palette.primary.main,
                        borderWidth: '2px'
                      }
                    }
                  }}
                />
              </Box>
            </Grid>

            {/* Vehicle Owner Name */}
            <Grid item xs={12} md={4} lg={4}>
              <Box sx={{ 
                display: 'flex', 
                flexDirection: 'column',
                mb: { xs: 1, md: 2 }
              }}>
                <Typography variant="caption" sx={{ 
                  mb: 0.5, 
                  color: theme.palette.text.secondary,
                  display: 'flex',
                  alignItems: 'center'
                }}>
                  <PersonIcon sx={{ fontSize: '0.9rem', mr: 0.5 }} />
                  Vehicle Owner Name
                </Typography>
                <TextField
                  value={vehicleOwnerName}
                  onChange={(e) => setVehicleOwnerName(e.target.value)}
                  placeholder="Enter vehicle owner name"
                  fullWidth
                  variant="outlined"
                  InputProps={{
                    sx: { height: '48px' }
                  }}
                  sx={{
                    '& .MuiOutlinedInput-root': {
                      backgroundColor: 'white',
                      borderRadius: '8px',
                      '&:hover .MuiOutlinedInput-notchedOutline': {
                        borderColor: theme.palette.primary.main,
                        borderWidth: '1px'
                      },
                      '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                        borderColor: theme.palette.primary.main,
                        borderWidth: '2px'
                      }
                    }
                  }}
                />
              </Box>
            </Grid>
            
            {/* Total Cases */}
            <Grid item xs={12} md={4} lg={4}>
              <Box sx={{ 
                display: 'flex', 
                flexDirection: 'column',
                mb: { xs: 1, md: 2 }
              }}>
                <Typography variant="caption" sx={{ 
                  mb: 0.5, 
                  color: theme.palette.text.secondary,
                  display: 'flex',
                  alignItems: 'center'
                }}>
                  <ReceiptIcon sx={{ fontSize: '0.9rem', mr: 0.5 }} />
                  Total Cases*
                </Typography>
                <TextField
                  value={totalCases}
                  onChange={(e) => {
                    const value = e.target.value;
                    setTotalCases(value);
                    setFormErrors(prev => ({ ...prev, totalCases: '' }));
                    
                    // Calculate total freight if both total cases and freight per box are available
                    if (value && !isNaN(value) && freightPerBox && !isNaN(freightPerBox)) {
                      const calculatedTotal = parseFloat(value) * parseFloat(freightPerBox);
                      setTotalFreight(calculatedTotal.toFixed(2));
                      setFormErrors(prev => ({ ...prev, totalFreight: '' }));
                    }
                  }}
                  type="number"
                  error={!!formErrors.totalCases}
                  helperText={formErrors.totalCases}
                  placeholder="Enter total cases"
                  fullWidth
                  variant="outlined"
                  InputProps={{
                    sx: { height: '48px' }
                  }}
                  sx={{
                    '& .MuiOutlinedInput-root': {
                      backgroundColor: 'white',
                      borderRadius: '8px',
                      '&:hover .MuiOutlinedInput-notchedOutline': {
                        borderColor: theme.palette.primary.main,
                        borderWidth: '1px'
                      },
                      '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                        borderColor: theme.palette.primary.main,
                        borderWidth: '2px'
                      }
                    }
                  }}
                />
              </Box>
            </Grid>
            
            {/* Freight Per Box */}
            <Grid item xs={12} md={4} lg={4}>
              <Box sx={{ 
                display: 'flex', 
                flexDirection: 'column',
                mb: { xs: 1, md: 2 }
              }}>
                <Typography variant="caption" sx={{ 
                  mb: 0.5, 
                  color: theme.palette.text.secondary,
                  display: 'flex',
                  alignItems: 'center'
                }}>
                  <ReceiptIcon sx={{ fontSize: '0.9rem', mr: 0.5 }} />
                  Freight Per Box*
                </Typography>
                <TextField
                  value={freightPerBox}
                  onChange={(e) => {
                    const value = e.target.value;
                    setFreightPerBox(value);
                    setFormErrors(prev => ({ ...prev, freightPerBox: '' }));
                    
                    // Calculate total freight if both total cases and freight per box are available
                    if (value && !isNaN(value) && totalCases && !isNaN(totalCases)) {
                      const calculatedTotal = parseFloat(totalCases) * parseFloat(value);
                      setTotalFreight(calculatedTotal.toFixed(2));
                      setFormErrors(prev => ({ ...prev, totalFreight: '' }));
                    }
                  }}
                  type="number"
                  error={!!formErrors.freightPerBox}
                  helperText={formErrors.freightPerBox}
                  placeholder="Enter freight per box"
                  fullWidth
                  variant="outlined"
                  InputProps={{
                    sx: { height: '48px' }
                  }}
                  sx={{
                    '& .MuiOutlinedInput-root': {
                      backgroundColor: 'white',
                      borderRadius: '8px',
                      '&:hover .MuiOutlinedInput-notchedOutline': {
                        borderColor: theme.palette.primary.main,
                        borderWidth: '1px'
                      },
                      '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                        borderColor: theme.palette.primary.main,
                        borderWidth: '2px'
                      }
                    }
                  }}
                />
              </Box>
            </Grid>
            
            {/* Total Freight */}
            <Grid item xs={12} md={4} lg={4}>
              <Box sx={{ 
                display: 'flex', 
                flexDirection: 'column',
                mb: { xs: 1, md: 2 }
              }}>
                <Typography variant="caption" sx={{ 
                  mb: 0.5, 
                  color: theme.palette.text.secondary,
                  display: 'flex',
                  alignItems: 'center'
                }}>
                  <ReceiptIcon sx={{ fontSize: '0.9rem', mr: 0.5 }} />
                  Total Freight*
                </Typography>
                <TextField
                  value={totalFreight}
                  onChange={(e) => {
                    setTotalFreight(e.target.value);
                    setFormErrors(prev => ({ ...prev, totalFreight: '' }));
                  }}
                  type="number"
                  error={!!formErrors.totalFreight}
                  helperText={formErrors.totalFreight}
                  placeholder="Calculated automatically"
                  fullWidth
                  variant="outlined"
                  InputProps={{
                    sx: { height: '48px' }
                  }}
                  sx={{
                    '& .MuiOutlinedInput-root': {
                      backgroundColor: 'white',
                      borderRadius: '8px',
                      '&:hover .MuiOutlinedInput-notchedOutline': {
                        borderColor: theme.palette.primary.main,
                        borderWidth: '1px'
                      },
                      '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                        borderColor: theme.palette.primary.main,
                        borderWidth: '2px'
                      }
                    }
                  }}
                />
              </Box>
            </Grid>
            
            {/* Advance */}
            <Grid item xs={12} md={4} lg={4}>
              <Box sx={{ 
                display: 'flex', 
                flexDirection: 'column',
                mb: { xs: 1, md: 2 }
              }}>
                <Typography variant="caption" sx={{ 
                  mb: 0.5, 
                  color: theme.palette.text.secondary,
                  display: 'flex',
                  alignItems: 'center'
                }}>
                  <ReceiptIcon sx={{ fontSize: '0.9rem', mr: 0.5 }} />
                  Advance Amount
                </Typography>
                <TextField
                  value={advance}
                  onChange={(e) => setAdvance(e.target.value)}
                  type="number"
                  placeholder="Enter advance amount"
                  fullWidth
                  variant="outlined"
                  InputProps={{
                    sx: { height: '48px' }
                  }}
                  sx={{
                    '& .MuiOutlinedInput-root': {
                      backgroundColor: 'white',
                      borderRadius: '8px',
                      '&:hover .MuiOutlinedInput-notchedOutline': {
                        borderColor: theme.palette.primary.main,
                        borderWidth: '1px'
                      },
                      '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                        borderColor: theme.palette.primary.main,
                        borderWidth: '2px'
                      }
                    }
                  }}
                />
              </Box>
            </Grid>
          </Grid>
          

        </Paper>
        )}
        {autofillLoading && !customerOptions.length && mode === 'create' && (
          <Box sx={{ 
            display: 'flex', 
            flexDirection: 'column',
            alignItems: 'center', 
            justifyContent: 'center', 
            py: 4,
            backgroundColor: 'white',
            borderRadius: '8px',
            border: `1px dashed ${theme.palette.divider}`
          }}>
            <CircularProgress size={32} sx={{ mb: 2, color: theme.palette.primary.main }} />
            <Typography variant="body2" color="text.secondary">
              Loading data, please wait...
            </Typography>
          </Box>
        )}
        
        {mode === 'create' && error && (
          <Alert 
            severity="error" 
            variant="filled"
            sx={{ 
              mt: 2,
              borderRadius: '8px',
              '& .MuiAlert-icon': {
                alignItems: 'center'
              }
            }}
          >
            {error}
          </Alert>
        )}
      </DialogContent>

      <DialogActions sx={{ 
        padding: '16px 24px', 
        justifyContent: 'space-between',
        backgroundColor: '#f8f9fa',
        borderTop: '1px solid rgba(0,0,0,0.08)'
      }}>
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <Chip 
            label="Required fields*" 
            size="small" 
            variant="outlined" 
            sx={{ 
              borderColor: theme.palette.text.disabled,
              color: theme.palette.text.secondary,
              fontSize: '0.75rem'
            }} 
          />
        </Box>
        <Box>
          <Button 
            onClick={handleClose} 
            variant="outlined" 
            sx={{ 
              mr: 2,
              borderRadius: '8px',
              textTransform: 'none',
              fontWeight: 500,
              px: 3,
              py: 1,
              borderColor: theme.palette.grey[300],
              color: theme.palette.text.primary,
              '&:hover': {
                borderColor: theme.palette.grey[400],
                backgroundColor: theme.palette.grey[50]
              }
            }}
          >
            Cancel
          </Button>
          {mode === 'create' ? (
            <Button 
              onClick={handleSubmit} 
              variant="contained" 
              color="primary" 
              disabled={isLoading}
              startIcon={isLoading ? <CircularProgress size={20} color="inherit" /> : <ReceiptIcon />}
              sx={{ 
                borderRadius: '8px',
                textTransform: 'none',
                fontWeight: 500,
                px: 3,
                py: 1,
                boxShadow: '0 4px 10px rgba(0,0,0,0.1)',
                '&:hover': {
                  boxShadow: '0 6px 12px rgba(0,0,0,0.15)'
                }
              }}
            >
              Generate Challan
            </Button>
          ) : (
            <Button 
              onClick={challanData ? handlePrint : fetchChallanData}
              variant="contained" 
              color="primary" 
              disabled={fetchingChallan || (!challanData && !challanDate)}
              startIcon={fetchingChallan ? 
                <CircularProgress size={20} color="inherit" /> : 
                (challanData ? <PrintIcon /> : <SearchIcon />)
              }
              sx={{ 
                borderRadius: '8px',
                textTransform: 'none',
                fontWeight: 500,
                px: 3,
                py: 1,
                boxShadow: '0 4px 10px rgba(0,0,0,0.1)',
                '&:hover': {
                  boxShadow: '0 6px 12px rgba(0,0,0,0.15)'
                }
              }}
            >
              {challanData ? 'Print Challan' : 'Search Challan'}
            </Button>
          )}
        </Box>
      </DialogActions>
    </Dialog>
    
    {/* Dispatch Challan Viewer for displaying fetched challan data */}
    <DispatchChallanViewer
      open={challanViewerOpen}
      onClose={() => setChallanViewerOpen(false)}
      challanData={challanData}
      isLoading={fetchingChallan}
      error={printFormErrors.general}
    />
  </React.Fragment>);
};

export default DispatchChallanModal;
