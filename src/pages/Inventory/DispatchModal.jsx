import React, { useState, useEffect } from 'react';

import CloseIcon from '@mui/icons-material/Close';
import EventNoteIcon from '@mui/icons-material/EventNote';
import InventoryIcon from '@mui/icons-material/Inventory';
import LocalShippingIcon from '@mui/icons-material/LocalShipping';
import SearchIcon from '@mui/icons-material/Search';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Button,
  Typography,
  Grid,
  Table,
  TableHead,
  TableBody,
  TableRow,
  TableCell,
  CircularProgress,
  Box,
  Alert,
  IconButton,
  Paper,
  Divider,
  Chip,
  useTheme,
  Checkbox,
  FormControlLabel,
  Autocomplete,
} from '@mui/material';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';

import { fetchInventoryAutofill } from '../../services/inventoryService';
import { useSiteValue } from '../../app/SiteContext';

/**
 * Modal for dispatching inventory items
 */
const DispatchModal = ({ open, onClose, inventoryItem, onSubmit, isLoading, error, onSuccess }) => {
  const theme = useTheme();
  const [lots, setLots] = useState([]);
  const [isFormValid, setIsFormValid] = useState(false);
  const [vehicleNumber, setVehicleNumber] = useState('');
  const [transactionDate, setTransactionDate] = useState(new Date());
  const [fullLoad, setFullLoad] = useState(false);
  const [vehicleSuggestions, setVehicleSuggestions] = useState([]);
  const [isAutofillLoading, setIsAutofillLoading] = useState(false);

    const siteContext = useSiteValue();
    const mandiId = siteContext?.mandiId;

  useEffect(() => {
    if (open && inventoryItem && inventoryItem.mandi_number_lots) {
      const initialLots = inventoryItem.mandi_number_lots.map(lot => {
        console.log('Processing lot:', lot);
        
        // Max available units
        const maxUnits = lot.units ? parseInt(lot.units) : 0;
        
        // Already dispatched units (previously loaded)
        let alreadyDispatchedUnits = 0;
        if (lot.loaded_box_count !== undefined && lot.loaded_box_count !== null) {
          alreadyDispatchedUnits = parseInt(lot.loaded_box_count);
        }
        
        return {
          id: lot.id,
          mandi_number_id: lot.mandi_number_id || inventoryItem.mandi_number_id,
          // Available total units
          max_box_count: maxUnits,
          // Previously dispatched units
          already_dispatched: alreadyDispatchedUnits,
          // New units to dispatch (start at 0)
          dispatched_units: 0,
          sku_grade: lot.sku_grade || 'N/A',
        };
      });
      setLots(initialLots);
      
      setVehicleNumber('');
      setTransactionDate(new Date());
      setFullLoad(false);
      validateForm(initialLots);
    } else {
      // Reset if modal is closed or no data
      setLots([]);
      setVehicleNumber('');
      setTransactionDate(new Date());
      setFullLoad(false);
      setIsFormValid(false);
      setVehicleSuggestions([]);
    }
  }, [open, inventoryItem]);
  
  // Fetch vehicle number suggestions
  const fetchVehicleSuggestions = async () => {
    try {
      setIsAutofillLoading(true);
      
      // Prepare params for API
      const params = {
        transaction_date: transactionDate,
        mandi_id: inventoryItem?.mandi_id
      };
      
      const data = await fetchInventoryAutofill(params);
      if (data && Array.isArray(data)) {
        const vehicleNumbers = data.map(item => 
          item.vehicle_number || ''
        ).filter(number => number.trim() !== '');
        
        setVehicleSuggestions(vehicleNumbers);
      }
    } catch (error) {
      console.error('Error fetching vehicle suggestions:', error);
    } finally {
      setIsAutofillLoading(false);
    }
  };

    useEffect(() => {
      if (open && mandiId) {
        fetchVehicleSuggestions();
      }
    }, [open, transactionDate, mandiId]);
    

  const validateForm = (lotsData) => {
    // Check if lots are valid
    const lotsValid = lotsData.every(lot => 
      lot.dispatched_units >= 0 && 
      lot.dispatched_units <= lot.max_box_count - lot.already_dispatched
    );
    
    // Check if required info is valid
    const requiredInfoValid = 
      vehicleNumber.trim() !== '' && 
      transactionDate !== null;
    
    const isValid = lotsValid && requiredInfoValid;
    setIsFormValid(isValid);
    return isValid;
  };

  const handleUpdateDispatchedUnits = (index, value) => {
    const updatedLots = [...lots];
    const numValue = parseInt(value, 10);
    updatedLots[index].dispatched_units = isNaN(numValue) ? 0 : numValue;
    setLots(updatedLots);
    validateForm(updatedLots);
  };
  
  const handleVehicleNumberChange = (event, newValue) => {
    setVehicleNumber(newValue || '');
    validateForm(lots);
  };
  
  const handleVehicleNumberInputChange = (event, newInputValue) => {
    setVehicleNumber(newInputValue);
    validateForm(lots);
  };
  
  const handleTransactionDateChange = (date) => {
    setTransactionDate(date);
    validateForm(lots);
  };
  
  const handleFullLoadChange = (e) => {
    const isChecked = e.target.checked;
    setFullLoad(isChecked);
    
    if (isChecked) {
      const updatedLots = lots.map(lot => ({
        ...lot,
        dispatched_units: Math.max(0, lot.max_box_count - lot.already_dispatched)
      }));
      setLots(updatedLots);
      validateForm(updatedLots);
    }
  };

  const handleSubmit = () => {
    if (validateForm(lots)) {
      const submitData = {
        vehicle_number: vehicleNumber.trim(),
        transaction_date: new Date(transactionDate.setHours(0, 0, 0, 0)).toISOString(),
        mandi_number_id: inventoryItem.id,
        lots: lots
          .filter(lot => lot.dispatched_units > 0)
          .map(lot => ({
            lot_id: lot.id,
            dispatched_units: lot.dispatched_units
          }))
      };
      onSubmit(submitData);
    }
  };

  if (!open) return null;

  return (
    <Dialog 
      open={open} 
      onClose={onClose}
      maxWidth='md'
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: '12px',
          boxShadow: '0 8px 32px rgba(0,0,0,0.15)',
          minHeight: '450px',
          overflow: 'hidden'
        }
      }}
    >
      <DialogTitle sx={{ 
        display: 'flex', 
        justifyContent: 'space-between', 
        alignItems: 'center',
        backgroundColor: theme.palette.primary.main,
        color: 'white',
        padding: '20px 24px',
        borderBottom: '1px solid rgba(255,255,255,0.1)'
      }}>
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <LocalShippingIcon sx={{ mr: 1.5, fontSize: '1.8rem' }} />
          <Typography variant='h6' component='div' sx={{ fontWeight: '600', letterSpacing: '0.3px' }}>
            Dispatch Units for Mandi #{inventoryItem?.mandi_number}
          </Typography>
        </Box>
        <IconButton 
          edge='end' 
          color='inherit' 
          onClick={onClose} 
          aria-label='close'
          size='small'
          sx={{ 
            backgroundColor: 'rgba(255,255,255,0.1)', 
            '&:hover': { backgroundColor: 'rgba(255,255,255,0.2)' } 
          }}
        >
          <CloseIcon />
        </IconButton>
      </DialogTitle>
      
      <DialogContent sx={{ 
        padding: '24px', 
        backgroundColor: '#f8f9fa',
        height: '100%',
        display: 'flex',
        flexDirection: 'column'
      }}>
        {error && (
          <Alert 
            severity='error' 
            variant='filled'
            sx={{ 
              mb: 2,
              borderRadius: '8px',
              '& .MuiAlert-icon': {
                alignItems: 'center'
              }
            }}
          >
            {error}
          </Alert>
        )}
        
        <Paper elevation={0} sx={{ 
          p: 3, 
          borderRadius: '12px', 
          boxShadow: '0 2px 12px rgba(0,0,0,0.08)',
          mb: 2,
          flex: 1
        }}>
          <Box sx={{ 
            display: 'flex', 
            alignItems: 'center', 
            mb: 3,
            pb: 2,
            borderBottom: `1px solid ${theme.palette.divider}`
          }}>
            <LocalShippingIcon sx={{ 
              mr: 2, 
              color: theme.palette.primary.main,
              fontSize: '1.5rem' 
            }} />
            <Typography variant='h6' sx={{ 
              fontWeight: 500,
              color: theme.palette.text.primary,
            }}>
              Vehicle Information
            </Typography>
          </Box>
          
          <Grid container spacing={3} sx={{ mb: 4 }}>
            <Grid item xs={12} md={6}>
              <Box sx={{ 
                display: 'flex', 
                flexDirection: 'column',
                mb: { xs: 1, md: 2 }
              }}>
                <Typography variant='caption' sx={{ 
                  mb: 0.5, 
                  color: theme.palette.text.secondary,
                  display: 'flex',
                  alignItems: 'center'
                }}>
                  <EventNoteIcon sx={{ fontSize: '0.9rem', mr: 0.5 }} />
                  Transaction Date*
                </Typography>
                <LocalizationProvider dateAdapter={AdapterDateFns}>
                  <DatePicker
                    value={transactionDate}
                    onChange={handleTransactionDateChange}
                    slotProps={{
                      textField: {
                        fullWidth: true,
                        variant: 'outlined',
                        placeholder: 'Select date',
                        size: 'medium',
                        sx: { 
                          '& .MuiInputBase-root': { 
                            height: '56px',
                            borderRadius: '8px',
                          },
                          '& .MuiOutlinedInput-root': {
                            backgroundColor: 'white',
                            '&:hover .MuiOutlinedInput-notchedOutline': {
                              borderColor: theme.palette.primary.main,
                              borderWidth: '1px'
                            },
                            '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                              borderColor: theme.palette.primary.main,
                              borderWidth: '2px'
                            }
                          }
                        }
                      }
                    }}
                  />
                </LocalizationProvider>
              </Box>
            </Grid>
            <Grid item xs={12} md={6}>
              <Box sx={{ 
                display: 'flex', 
                flexDirection: 'column',
                mb: { xs: 1, md: 2 }
              }}>
                <Typography variant='caption' sx={{ 
                  mb: 0.5, 
                  color: theme.palette.text.secondary,
                  display: 'flex',
                  alignItems: 'center'
                }}>
                  <LocalShippingIcon sx={{ fontSize: '0.9rem', mr: 0.5 }} />
                  Vehicle Number*
                </Typography>
                <Autocomplete
                  fullWidth
                  freeSolo
                  options={vehicleSuggestions}
                  value={vehicleNumber}
                  onChange={handleVehicleNumberChange}
                  onInputChange={handleVehicleNumberInputChange}
                  loading={isAutofillLoading}
                  renderInput={(params) => (
                    <TextField
                      {...params}
                      placeholder='Enter vehicle number'
                      required
                      error={vehicleNumber.trim() === ''}
                      helperText={
                      <Box sx={{ minHeight: '20px' }}>
                        {vehicleNumber.trim() === '' ? 'Vehicle number is required' : ''}
                      </Box>
                    }
                    variant='outlined'
                    InputProps={{
                      ...params.InputProps,
                      startAdornment: (
                        <>
                          <SearchIcon color='action' sx={{ mr: 1, ml: 0.5, opacity: 0.6 }} />
                          {params.InputProps.startAdornment}
                        </>
                      ),
                      endAdornment: (
                        <>
                          {isAutofillLoading ? <CircularProgress color='inherit' size={20} /> : null}
                          {params.InputProps.endAdornment}
                        </>
                      ),
                      sx: {
                        height: '56px !important', // force height
                        borderRadius: '8px',
                        boxSizing: 'border-box',
                        '& input': {
                          height: '100% !important',
                          width: '100% !important',
                          padding: '14px 0 !important',
                          boxSizing: 'border-box'
                        }
                      }
                    }}
                  />
                )}
                sx={{
                  width: '100%',
                  '& .MuiOutlinedInput-root': {
                    backgroundColor: 'white',
                    height: '56px !important',
                    boxSizing: 'border-box',
                    '& input': {
                      height: '100% !important',
                      boxSizing: 'border-box'
                    },
                    '&:hover .MuiOutlinedInput-notchedOutline': {
                      borderColor: theme.palette.primary.main,
                      borderWidth: '1px'
                    },
                    '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                      borderColor: theme.palette.primary.main,
                      borderWidth: '2px'
                    }
                  }
                }}
                ListboxProps={{
                  sx: {
                    '& .MuiAutocomplete-option': {
                      borderBottom: `1px solid ${theme.palette.divider}`,
                      '&:last-child': {
                        borderBottom: 'none'
                      }
                    }
                    }
                  }}
                />
              </Box>
            </Grid>
          </Grid>
          
          <Box sx={{ 
            display: 'flex', 
            alignItems: 'center', 
            justifyContent: 'space-between',
            mb: 3,
            pb: 2,
            pt: 2,
            borderBottom: `1px solid ${theme.palette.divider}`,
            borderTop: `1px solid ${theme.palette.divider}`
          }}>
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <InventoryIcon sx={{ 
                mr: 2, 
                color: theme.palette.primary.main,
                fontSize: '1.5rem' 
              }} />
              <Typography variant='h6' sx={{ 
                fontWeight: 500,
                color: theme.palette.text.primary,
              }}>
                Dispatch Units
              </Typography>
            </Box>
            <FormControlLabel
              control={
                <Checkbox
                  checked={fullLoad}
                  onChange={handleFullLoadChange}
                  sx={{
                    color: theme.palette.primary.main,
                    '&.Mui-checked': {
                      color: theme.palette.primary.main,
                    },
                  }}
                />
              }
              label={
                <Typography variant='body2' sx={{ fontWeight: 500, color: theme.palette.text.primary }}>
                  Full Load
                </Typography>
              }
              sx={{
                '& .MuiFormControlLabel-label': {
                  fontSize: '0.875rem',
                },
                marginRight: 0,
              }}
            />
          </Box>
        
        {lots.length > 0 ? (
          <Paper elevation={0} sx={{ 
            borderRadius: '8px', 
            overflow: 'hidden',
            border: `1px solid ${theme.palette.divider}`,
          }}>
            <Table>
              <TableHead sx={{ backgroundColor: 'rgba(0, 0, 0, 0.03)' }}>
                <TableRow>
                  <TableCell sx={{ width: 100 }}>Lot ID</TableCell>
                  <TableCell sx={{ width: 120 }}>Grade</TableCell>
                  <TableCell sx={{ width: 150 }}>Total Sold Units</TableCell>
                  <TableCell sx={{ width: 150 }}>Dispatched Units</TableCell>
                  <TableCell sx={{ width: 200 }}>Load Units</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {lots.map((lot, index) => (
                  <TableRow key={lot.id} sx={{
                    '&:nth-of-type(odd)': { backgroundColor: 'rgba(0, 0, 0, 0.01)' },
                    '&:hover': { backgroundColor: 'rgba(0, 0, 0, 0.04)' },
                  }}>
                    <TableCell>{lot.id}</TableCell>
                    <TableCell>
                      <Chip 
                        label={lot.size || lot.sku_grade} 
                        size='small' 
                        sx={{ 
                          backgroundColor: theme.palette.primary.light,
                          color: theme.palette.primary.contrastText,
                          fontWeight: 500,
                          fontSize: '0.75rem'
                        }} 
                      />
                    </TableCell>
                    <TableCell>{lot.max_box_count}</TableCell>
                    <TableCell>{lot.already_dispatched}</TableCell>
                    <TableCell>
                      <Grid container direction='column' spacing={1}>
                        <Grid item>
                          {lot.max_box_count > lot.already_dispatched ? (
                            <TextField
                              type='number'
                              placeholder='Units to dispatch'
                              value={lot.dispatched_units}
                              onChange={(e) => handleUpdateDispatchedUnits(index, e.target.value)}
                              inputProps={{ 
                                min: 0,
                                max: lot.max_box_count - lot.already_dispatched
                              }}
                              error={lot.dispatched_units > (lot.max_box_count - lot.already_dispatched) || lot.dispatched_units < 0}
                              size='small'
                              variant='outlined'
                              fullWidth
                              sx={{
                                '& .MuiOutlinedInput-root': {
                                  backgroundColor: 'white',
                                  borderRadius: '8px',
                                  '&:hover .MuiOutlinedInput-notchedOutline': {
                                    borderColor: theme.palette.primary.main,
                                  },
                                  '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                                    borderColor: theme.palette.primary.main,
                                  }
                                }
                              }}
                            />
                          ) : (
                            <Box sx={{ 
                              p: 1, 
                              bgcolor: '#f5f5f5', 
                              border: '1px solid #e0e0e0',
                              borderRadius: '8px',
                              color: 'text.disabled',
                              display: 'flex',
                              alignItems: 'center',
                              justifyContent: 'space-between'
                            }}>
                              <Typography variant='body2' sx={{ fontStyle: 'italic' }}>
                                All units dispatched
                              </Typography>
                              <Typography variant='body2' sx={{ fontWeight: 'bold' }}>
                                0
                              </Typography>
                            </Box>
                          )}
                        </Grid>
                        {lot.max_box_count > lot.already_dispatched && lot.dispatched_units > (lot.max_box_count - lot.already_dispatched) && (
                          <Grid item sx={{ minHeight: 20 }}>
                            <Typography variant='caption' color='error'>
                              Cannot exceed remaining units ({lot.max_box_count - lot.already_dispatched})
                            </Typography>
                          </Grid>
                        )}
                        {lot.dispatched_units < 0 && (
                          <Grid item sx={{ minHeight: 20 }}>
                            <Typography variant='caption' color='error'>
                              Value cannot be negative
                            </Typography>
                          </Grid>
                        )}
                      </Grid>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </Paper>
        ) : (
          <Box sx={{ 
            p: 3, 
            textAlign: 'center', 
            backgroundColor: 'rgba(0,0,0,0.02)',
            borderRadius: '8px',
            border: `1px dashed ${theme.palette.divider}`
          }}>
            <Typography variant='body1' color='text.secondary'>
              No lots available for this mandi number.
            </Typography>
          </Box>
        )}
        </Paper>
      </DialogContent>
      
      <DialogActions sx={{ 
        padding: '16px 24px', 
        justifyContent: 'space-between',
        backgroundColor: '#f8f9fa',
        borderTop: '1px solid rgba(0,0,0,0.08)'
      }}>
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <Chip 
            label='Required fields*' 
            size='small' 
            variant='outlined' 
            sx={{ 
              borderColor: theme.palette.text.disabled,
              color: theme.palette.text.secondary,
              fontSize: '0.75rem'
            }} 
          />
        </Box>
        <Box>
          <Button 
            onClick={onClose} 
            variant='outlined' 
            sx={{ 
              mr: 2,
              borderRadius: '8px',
              textTransform: 'none',
              fontWeight: 500,
              px: 3,
              py: 1,
              borderColor: theme.palette.grey[300],
              color: theme.palette.text.primary,
              '&:hover': {
                borderColor: theme.palette.grey[400],
                backgroundColor: theme.palette.grey[50]
              }
            }}
          >
            Cancel
          </Button>
          <Button 
            onClick={handleSubmit} 
            color='primary' 
            variant='contained'
            disabled={!isFormValid || isLoading}
            sx={{ 
              borderRadius: '8px',
              textTransform: 'none',
              fontWeight: 500,
              px: 3,
              py: 1,
              boxShadow: '0 4px 10px rgba(0,0,0,0.1)',
              '&:hover': {
                boxShadow: '0 6px 12px rgba(0,0,0,0.15)'
              }
            }}
          >
            {isLoading ? (
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <CircularProgress size={20} sx={{ mr: 1 }} />
                Updating...
              </Box>
            ) : 'Submit'}
          </Button>
        </Box>
      </DialogActions>
    </Dialog>
  );
};

export default DispatchModal;
