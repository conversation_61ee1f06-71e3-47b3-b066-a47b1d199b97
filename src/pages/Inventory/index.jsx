import React, { useState, useEffect } from 'react';
import { useSiteValue } from '../../app/SiteContext';
import { useSnackbar } from 'notistack';
import {
  Box,
  Tabs,
  Tab,
  Paper,
  Typography,
  CircularProgress,
  Grid,
  TextField,
  Button,
  Divider,
} from '@mui/material';
import ReceiptIcon from '@mui/icons-material/Receipt';

import PageLayout from '../../components/PageLayout';
import NoDataAvailable from '../../components/NoDataAvailable';
import { fetchInventoryData } from '../../services/inventoryService';
import InventoryTable from './InventoryTable';
import DispatchChallanModal from './DispatchChallanModal';
import DispatchChallanViewer from './DispatchChallanViewer';
import { generateDispatchChallan } from '../../services/inventoryService';

// Constants for tab values
const TAB_VALUES = {
  YET_TO_LOAD: 'YET_TO_LOAD',
  PARTIALLY_LOADED: 'PARTIALLY_LOADED',
  FULLY_LOADED: 'FULLY_LOADED',
};

const Inventory = () => {
  // State variables for inventory data, loading status, etc.
  const [inventoryData, setInventoryData] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [activeTab, setActiveTab] = useState(TAB_VALUES.YET_TO_LOAD);
  const { enqueueSnackbar } = useSnackbar();

  // State for filters
  const [token, setToken] = useState('');
  const [farmerName, setFarmerName] = useState('');
  const [customerName, setCustomerName] = useState('');
  const [mandiNumber, setMandiNumber] = useState('');
  
  // State for dispatch challan modal and viewer
  const [dispatchChallanModalOpen, setDispatchChallanModalOpen] = useState(false);
  const [dispatchChallanViewerOpen, setDispatchChallanViewerOpen] = useState(false);
  const [challanData, setChallanData] = useState(null);
  const [challanLoading, setChallanLoading] = useState(false);
  const [challanError, setChallanError] = useState(null);

  // Get global mandi_id and auction_date from SiteContext
  const { mandiId, auctionDate } = useSiteValue();

  const [appliedFilters, setAppliedFilters] = useState({
    token: '',
    farmer_name: '',
    customer_name: '',
    mandi_number: '',
  });
  

  // Handle tab change
  const handleTabChange = (event, newTab) => {
    setActiveTab(newTab);
  };
  
  // Function to fetch inventory data
  const fetchData = async () => {
    setIsLoading(true);
    setError(null);
    try {
      const params = { loading_status: activeTab };
      const { token, farmer_name, customer_name, mandi_number } = appliedFilters;
      if (token) params.token = token;
      if (farmer_name) params.farmer_name = farmer_name;
      if (customer_name) params.customer_name = customer_name;
      if (mandi_number) params.mandi_number = mandi_number;
      
      // Add global mandi_id and auction_date to the params
      if (mandiId) params.mandi_id = mandiId;
      if (auctionDate) {
        // Convert auctionDate to timestamp format
        const auctionTimestamp = new Date(auctionDate).getTime();
        params.auction_date = auctionTimestamp;
      }
      
      console.log('Fetching inventory with params:', params);
      const data = await fetchInventoryData(params);
      console.log('Setting inventory data:', data.items);
      setInventoryData(data.items || []);
    } catch (err) {
      console.error('Error fetching inventory data:', err);
      setError('Failed to fetch inventory data. Please try again.');
      setInventoryData([]);
    } finally {
      setIsLoading(false);
    }
  };

  // Handle filter changes
  const handleTokenChange = (event) => {
    setToken(event.target.value);
  };

  const handleFarmerNameChange = (event) => {
    setFarmerName(event.target.value);
  };

  const handleCustomerNameChange = (event) => {
    setCustomerName(event.target.value);
  };


  const handleMandiNumberChange = (event) => {
    setMandiNumber(event.target.value);
  };

  // Apply filters
  const handleApplyFilters = () => {
    setAppliedFilters({
      token,
      farmer_name: farmerName,
      customer_name: customerName,
      mandi_number: mandiNumber,
    });
  };

  // Clear filters
  const handleClearFilters = () => {
    setToken('');
    setFarmerName('');
    setCustomerName('');
    setMandiNumber('');
    
    setAppliedFilters({
      token: '',
      farmer_name: '',
      customer_name: '',
      mandi_number: '',
    });
  };
  
  // Handler for opening dispatch challan modal
  const handleOpenDispatchChallanModal = () => {
    setDispatchChallanModalOpen(true);
  };

  // Handler for closing dispatch challan modal
  const handleCloseDispatchChallanModal = () => {
    setDispatchChallanModalOpen(false);
  };

  // Handler for submitting dispatch challan request
  const handleSubmitDispatchChallan = async (formData) => {
    setChallanLoading(true);
    setChallanError(null);
    
    try {
      // Convert date to timestamp
      const timestamp = new Date(formData.transaction_date).getTime();
      const requestData = {
        ...formData,
        transaction_date: timestamp
      };
      
      const response = await generateDispatchChallan(requestData);
      console.log('Dispatch challan data:', response);
      setChallanData(response);
      setDispatchChallanModalOpen(false);
      setDispatchChallanViewerOpen(true);

      enqueueSnackbar('Dispatch Challan generated successfully', { variant: 'success' });
    } catch (error) {
      console.error('Error fetching dispatch challan:', error);
      setChallanError('Failed to fetch dispatch challan. Please try again.');
    } finally {
      setChallanLoading(false);
    }
  };

  // Handler for closing dispatch challan viewer
  const handleCloseDispatchChallanViewer = () => {
    setDispatchChallanViewerOpen(false);
    // Keep the challan data in case user wants to reopen
  };

  // Fetch data when filters, tab, or auction date changes
  useEffect(() => {
    fetchData();
  }, [appliedFilters, activeTab, auctionDate]);

  return (
    <PageLayout title="Inventory">
      <PageLayout.Body
        sx={{
          padding: { xs: 2, sm: 3, md: 4 },
          minHeight: 'calc(100vh - 120px)',
        }}
      >
        <Paper sx={{ p: 3, mb: 3 }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
            <Typography variant="h6">
              Inventory Filters
            </Typography>
            <Button
              variant="contained"
              color="secondary"
              onClick={handleOpenDispatchChallanModal}
              startIcon={<ReceiptIcon />}
              sx={{
                textTransform: 'none',
                fontWeight: 'medium',
                boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
                '&:hover': {
                  boxShadow: '0 4px 8px rgba(0,0,0,0.15)'
                },
                px: 2
              }}
            >
              Get Dispatch Challan
            </Button>
          </Box>
          
          <Divider sx={{ my: 2 }} />
          
          <Grid container spacing={3} alignItems="center">
            <Grid item xs={12} sm={6} md={2.5}>
              <TextField
                fullWidth
                label="Token"
                value={token}
                onChange={handleTokenChange}
                variant="outlined"
              />
            </Grid>
            <Grid item xs={12} sm={6} md={2.5}>
              <TextField
                fullWidth
                label="Farmer Name"
                value={farmerName}
                onChange={handleFarmerNameChange}
                variant="outlined"
              />
            </Grid>
            <Grid item xs={12} sm={6} md={2.5}>
              <TextField
                fullWidth
                label="Customer Name"
                value={customerName}
                onChange={handleCustomerNameChange}
                variant="outlined"
              />
            </Grid>
            <Grid item xs={12} sm={6} md={2.5}>
              <TextField
                fullWidth
                label="Mandi Number"
                value={mandiNumber}
                onChange={handleMandiNumberChange}
                variant="outlined"
              />
            </Grid>
            
            {/* Filter action buttons - positioned at the right end in the same row */}
            <Grid item xs={12} md={2} sx={{ display: 'flex', justifyContent: 'flex-end', gap: 1 }}>
              <Button
                variant="outlined"
                color="primary"
                onClick={handleClearFilters}
                size="medium"
              >
                Clear
              </Button>
              <Button
                variant="contained"
                color="primary"
                onClick={handleApplyFilters}
                size="medium"
              >
                Apply
              </Button>
            </Grid>
          </Grid>
        </Paper>

        <Paper sx={{ width: '100%', mb: 3 }}>
          <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
            <Tabs 
              value={activeTab} 
              onChange={handleTabChange}
              aria-label="inventory tabs"
              sx={{
                '& .MuiTab-root': {
                  fontWeight: 'bold',
                }
              }}
            >
              <Tab label="Yet to Load" value={TAB_VALUES.YET_TO_LOAD} />
              <Tab label="Partially Loaded" value={TAB_VALUES.PARTIALLY_LOADED} />
              <Tab label="Fully Loaded" value={TAB_VALUES.FULLY_LOADED} />
            </Tabs>
          </Box>
          
          {isLoading ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
              <CircularProgress />
            </Box>
          ) : error ? (
            <Box sx={{ p: 4 }}>
              <Typography color="error">{error}</Typography>
            </Box>
          ) : inventoryData.length > 0 ? (
            <InventoryTable 
              data={inventoryData} 
              status={activeTab}
              onRefresh={fetchData}
            />
          ) : (
            <Box sx={{ p: 4 }}>
              <NoDataAvailable 
                message={token || farmerName || customerName || mandiNumber ? 'No inventory data found for the selected filters' : 'Please apply filters to view inventory data'}
              />
            </Box>
          )}
        </Paper>
      </PageLayout.Body>
      
      <DispatchChallanModal
        open={dispatchChallanModalOpen}
        onClose={handleCloseDispatchChallanModal}
        onSubmit={handleSubmitDispatchChallan}
        isLoading={challanLoading}
        error={challanError}
      />

      <DispatchChallanViewer
        open={dispatchChallanViewerOpen}
        onClose={handleCloseDispatchChallanViewer}
        challanData={challanData}
        isLoading={challanLoading}
        error={challanError}
      />
    </PageLayout>
  );
};

export default Inventory;
