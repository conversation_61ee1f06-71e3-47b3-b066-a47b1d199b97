import React, { forwardRef } from 'react';

import { getDate } from '../../../utilities/dateUtils';

import {
  Container,
  HeaderSection,
  DateField,
  DateBox,
  DateText,
  TitleSection,
  MainTitle,
  LogoSection,
  BrandLogo,
  BrandText,
  FruitXText,
  XText,
  CompanyText,
  InfoGrid,
  InfoBox,
  InfoLabel,
  InfoValue,
  SignaturesSection,
  SignatureBox,
  SignatureLabel,
  SignatureLine,
  TimestampSection,
} from './style';

const PrintCustomerSlip = forwardRef(
  (
    {
      tokenNumber,
      farmerName,
      mandiNo,
      date,
      brandLogo,
      lotData,
      pageData, // New prop for multiple pages (old format - 1 per page)
      pairedPageData, // New prop for paired pages (2 per page)
      skuSizes = [], // Add skuSizes prop for dynamic headers
      customerShortCode, // Add customer_short_code prop
      customerName, // Add customer_name prop
      vehicleNumber, // Add vehicle number prop
    },
    ref
  ) => {
    // Function to format current timestamp
    const getCurrentTimestamp = () => {
      const now = new Date();
      const options = {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        hour12: true,
        timeZone: 'Asia/Calcutta',
      };
      return now.toLocaleString('en-IN', options);
    };

    // Get grade columns from SKU sizes API response - completely dynamic
    const getGradeColumns = lots => {
      // If we have skuSizes from API, use them
      if (skuSizes && skuSizes.length > 0) {
        const sortedSkuSizes = skuSizes.sort((a, b) => a.id - b.id);
        return sortedSkuSizes;
      }

      // Fallback: extract from lot data if no skuSizes provided
      if (!lots || lots.length === 0) {
        return [];
      }

      const gradeKeys = new Set();
      lots.forEach(lot => {
        if (lot.grades) {
          Object.keys(lot.grades).forEach(gradeKey => {
            gradeKeys.add(gradeKey);
          });
        }
      });

      const fallbackColumns = Array.from(gradeKeys)
        .sort()
        .map(key => ({ id: key, size: key }));
      return fallbackColumns;
    };

    // Split lots into pages to ensure proper header/footer on each page (like token slip)
    const LOTS_PER_PAGE = 5; // Maximum 5 varieties per page to ensure proper header and footer spacing

    // Helper function to get header display name - use API data directly
    const getHeaderDisplay = size => {
      // Use the size directly from API - no hardcoded mapping needed
      return size;
    };

    const renderDataRows = lots => {
      if (!lots) return null;

      const gradeColumns = getGradeColumns(lots);
      const rows = [];

      // Calculate total units for display beside mandi number
      const getTotalUnits = lot => {
        if (!lot.grades) return 0;
        return Object.values(lot.grades).reduce((total, grade) => {
          return total + (parseInt(grade.quantity) || 0);
        }, 0);
      };

      lots.forEach((lot, index) => {
        const totalUnits = getTotalUnits(lot);

        // Quantity row
        rows.push(
          <tr key={`quantity-row-${index}`}>
            <td className='mandi-no' rowSpan={2}>
              <div className='mandi-no-content'>
                <div className='mandi-number-bold'>{lot.mandiNo || ''}</div>
                {totalUnits > 0 && (
                  <div className='total-count'>/{totalUnits}</div>
                )}
              </div>
            </td>
            <td className='variety' rowSpan={2}>
              {lot.variety || ''}
            </td>
            <td className='lot' rowSpan={2}>
              <div>
                <div>{lot.lot || ''}</div>
                <div
                  style={{ fontSize: '8px', marginTop: '2px', color: '#666' }}
                >
                  {lot.packType || ''}
                </div>
              </div>
            </td>
            <td className='qty-label-bold'>Qty</td>
            {/* Dynamic grade columns from API */}
            {gradeColumns.map(column => {
              const quantity = lot.grades?.[column.size]?.quantity;
              return (
                <td
                  key={`qty-${column.id}`}
                  className='grade-data qty-value-bold'
                >
                  {quantity || '-'}
                </td>
              );
            })}
          </tr>
        );

        // Price row
        rows.push(
          <tr key={`price-row-${index}`}>
            <td className='price-label-bold'>Price-</td>
            {/* Dynamic grade columns from API */}
            {gradeColumns.map(column => {
              const price = lot.grades?.[column.size]?.price;
              return (
                <td
                  key={`price-${column.id}`}
                  className='grade-data price-value-bold'
                >
                  {price || '-'}
                </td>
              );
            })}
          </tr>
        );
      });
      return rows;
    };

    // Render header section (reusable for each page)
    const renderHeader = pageInfo => {
      const displayCustomerCode =
        pageInfo?.customerShortCode || customerShortCode || '';

      return (
        <>
          {/* Header Section */}
          <HeaderSection>
            {/* Date Field */}
            <DateField>
              <DateBox>
                <DateText>Date: {date ? getDate(date) : ''}</DateText>
              </DateBox>
            </DateField>

            {/* Title */}
            <TitleSection>
              <MainTitle>Customer Slip</MainTitle>
            </TitleSection>

            {/* Logo */}
            <LogoSection>
              {brandLogo ? (
                <>
                  <BrandLogo src={brandLogo} alt='Brand Logo' />
                  <CompanyText>Chifu Agritech Pvt Ltd</CompanyText>
                </>
              ) : (
                <>
                  <BrandText>
                    <FruitXText>FRUIT</FruitXText>
                    <XText>X</XText>
                  </BrandText>
                  <CompanyText>Chifu Agritech Pvt Ltd</CompanyText>
                </>
              )}
            </LogoSection>
          </HeaderSection>

          {/* Info Section */}
          <InfoGrid>
            <InfoBox>
              <InfoLabel>Token No. (टोकन संख्या)</InfoLabel>
              <InfoValue className='token-number-bold'>
                {tokenNumber || ''}
              </InfoValue>
            </InfoBox>

            <InfoBox>
              <InfoLabel>Farmer Name (किसान का नाम)</InfoLabel>
              <InfoValue>{farmerName || ''}</InfoValue>
            </InfoBox>

            <InfoBox>
              <InfoLabel>Vehicle Number (गाडी नंबर)</InfoLabel>
              <InfoValue className='vehicle-number-bold'>
                {vehicleNumber || ''}
              </InfoValue>
            </InfoBox>

            <InfoBox>
              <InfoLabel>Customer Code</InfoLabel>
              <InfoValue>{displayCustomerCode}</InfoValue>
            </InfoBox>
          </InfoGrid>
        </>
      );
    };

    // Render footer section (reusable for each page)
    const renderFooter = pageInfo => {
      const displayName = pageInfo?.customerName || customerName || '';

      return (
        <>
          {/* Footer Section */}
          <SignaturesSection>
            <SignatureBox>
              <SignatureLabel>
                Loader Name and Sign
                <br />
                (हस्ताक्षर)
                <br />
                {displayName}
              </SignatureLabel>
              <SignatureLine></SignatureLine>
            </SignatureBox>

            <SignatureBox>
              <SignatureLabel>
                Supervisor Name and Sign
                <br />
                (हस्ताक्षर)
              </SignatureLabel>
              <SignatureLine></SignatureLine>
            </SignatureBox>
          </SignaturesSection>

          {/* Timestamp Section */}
          <TimestampSection>
            Generated on {getCurrentTimestamp()}
          </TimestampSection>
        </>
      );
    };

    // If pairedPageData is provided, render paired slips (2 per page)
    if (pairedPageData && pairedPageData.length > 0) {
      return (
        <div ref={ref}>
          {pairedPageData.map((slipPair, index) => {
            const hasSecondSlip = slipPair[1];
            const isLastPage = index === pairedPageData.length - 1;

            return (
              <div
                key={`paired-page-${index}`}
                style={{
                  pageBreakAfter: isLastPage ? 'auto' : 'always',
                }}
              >
                {/* First slip - ORIGINAL SIZE with header and footer */}
                <Container
                  key={`slip-${index}-0`}
                  style={{ pageBreakAfter: 'auto' }}
                >
                  {renderHeader(slipPair[0])}
                  <div className='main-table'>
                    <div className='watermark'>FRUITX</div>
                    <table className='customer-table'>
                      <thead>
                        <tr>
                          <th rowSpan='3'>Mandi No.</th>
                          <th rowSpan='3'>Variety</th>
                          <th rowSpan='3'>Lot</th>
                          <th></th>
                          {getGradeColumns(slipPair[0].lotData).map(column => (
                            <th key={`header-${column.id}-${index}-0`}>
                              {getHeaderDisplay(column.size)}
                            </th>
                          ))}
                        </tr>
                      </thead>
                      <tbody>{renderDataRows(slipPair[0].lotData)}</tbody>
                    </table>
                  </div>
                  {renderFooter(slipPair[0])}
                </Container>

                {/* Second slip - ORIGINAL SIZE with header and footer - only if exists */}
                {hasSecondSlip && (
                  <div style={{ marginTop: '80px' }}>
                    <Container
                      key={`slip-${index}-1`}
                      style={{ pageBreakAfter: 'auto' }}
                    >
                      {renderHeader(slipPair[1])}
                      <div className='main-table'>
                        <div className='watermark'>FRUITX</div>
                        <table className='customer-table'>
                          <thead>
                            <tr>
                              <th rowSpan='3'>Mandi No.</th>
                              <th rowSpan='3'>Variety</th>
                              <th rowSpan='3'>Lot</th>
                              <th></th>
                              {getGradeColumns(slipPair[1].lotData).map(
                                column => (
                                  <th key={`header-${column.id}-${index}-1`}>
                                    {getHeaderDisplay(column.size)}
                                  </th>
                                )
                              )}
                            </tr>
                          </thead>
                          <tbody>{renderDataRows(slipPair[1].lotData)}</tbody>
                        </table>
                      </div>
                      {renderFooter(slipPair[1])}
                    </Container>
                  </div>
                )}
              </div>
            );
          })}
        </div>
      );
    }

    // If pageData is provided, render multiple pages with pagination support
    if (pageData && pageData.length > 0) {
      // Create paginated pages for each customer (like token slip)
      const allPages = [];

      pageData.forEach((customerPage, customerIndex) => {
        const customerLots = customerPage.lotData || [];

        // Split lots into pages to ensure header and footer fit on each page
        for (let i = 0; i < customerLots.length; i += LOTS_PER_PAGE) {
          const lotsForPage = customerLots.slice(i, i + LOTS_PER_PAGE);

          allPages.push({
            ...customerPage,
            lotData: lotsForPage,
            pageNumber: Math.floor(i / LOTS_PER_PAGE) + 1,
            totalPages: Math.ceil(customerLots.length / LOTS_PER_PAGE),
            customerIndex: customerIndex,
          });
        }
      });

      return (
        <div ref={ref}>
          {allPages.map((pageInfo, index) => {
            const gradeColumns = getGradeColumns(pageInfo.lotData);
            const isLastPage = index === allPages.length - 1;

            return (
              <Container
                key={`page-${index}`}
                style={{ pageBreakAfter: isLastPage ? 'auto' : 'always' }}
              >
                {renderHeader(pageInfo)}

                {/* Main Table */}
                <div className='main-table'>
                  <div className='watermark'>FRUITX</div>
                  <table className='customer-table'>
                    <thead>
                      <tr>
                        <th rowSpan='3'>Mandi No.</th>
                        <th rowSpan='3'>Variety</th>
                        <th rowSpan='3'>Lot</th>
                        <th></th>
                        {/* Dynamic headers from API */}
                        {gradeColumns.map(column => (
                          <th key={`header-${column.id}`}>
                            {getHeaderDisplay(column.size)}
                          </th>
                        ))}
                      </tr>
                    </thead>
                    <tbody>{renderDataRows(pageInfo.lotData)}</tbody>
                    <tfoot></tfoot>
                  </table>
                </div>

                {renderFooter(pageInfo)}
              </Container>
            );
          })}
        </div>
      );
    }

    // Single page rendering with pagination support (backward compatibility)
    if (lotData && lotData.length > 0) {
      // Split lots into pages if there are too many for one page
      const allPages = [];

      for (let i = 0; i < lotData.length; i += LOTS_PER_PAGE) {
        const lotsForPage = lotData.slice(i, i + LOTS_PER_PAGE);

        allPages.push({
          mandiNo,
          lotData: lotsForPage,
          customerShortCode,
          pageNumber: Math.floor(i / LOTS_PER_PAGE) + 1,
          totalPages: Math.ceil(lotData.length / LOTS_PER_PAGE),
        });
      }

      return (
        <div ref={ref}>
          {allPages.map((pageInfo, index) => {
            const gradeColumns = getGradeColumns(pageInfo.lotData);
            const isLastPage = index === allPages.length - 1;

            return (
              <Container
                key={`page-${index}`}
                style={{ pageBreakAfter: isLastPage ? 'auto' : 'always' }}
              >
                {renderHeader(pageInfo)}

                {/* Main Table */}
                <div className='main-table'>
                  <div className='watermark'>FRUITX</div>
                  <table className='customer-table'>
                    <thead>
                      <tr>
                        <th rowSpan='3'>Mandi No.</th>
                        <th rowSpan='3'>Variety</th>
                        <th rowSpan='3'>Lot</th>
                        <th></th>
                        {/* Dynamic headers from API */}
                        {gradeColumns.map(column => (
                          <th key={`header-${column.id}`}>
                            {getHeaderDisplay(column.size)}
                          </th>
                        ))}
                      </tr>
                    </thead>
                    <tbody>{renderDataRows(pageInfo.lotData)}</tbody>
                    <tfoot></tfoot>
                  </table>
                </div>

                {renderFooter(pageInfo)}
              </Container>
            );
          })}
        </div>
      );
    }

    // Fallback - empty page with header and footer
    const pageInfo = { mandiNo, lotData: [], customerShortCode };
    const gradeColumns = getGradeColumns([]);

    return (
      <div ref={ref}>
        <Container style={{ pageBreakAfter: 'auto' }}>
          {renderHeader(pageInfo)}

          {/* Main Table */}
          <div className='main-table'>
            <div className='watermark'>FRUITX</div>
            <table className='customer-table'>
              <thead>
                <tr>
                  <th rowSpan='3'>Mandi No.</th>
                  <th rowSpan='3'>Variety</th>
                  <th rowSpan='3'>Lot</th>
                  <th></th>
                  {/* Dynamic headers from API */}
                  {gradeColumns.map(column => (
                    <th key={`header-${column.id}`}>
                      {getHeaderDisplay(column.size)}
                    </th>
                  ))}
                </tr>
              </thead>
              <tbody>{renderDataRows([])}</tbody>
              <tfoot></tfoot>
            </table>
          </div>

          {renderFooter(pageInfo)}
        </Container>
      </div>
    );
  }
);

PrintCustomerSlip.displayName = 'PrintCustomerSlip';

export default PrintCustomerSlip;
