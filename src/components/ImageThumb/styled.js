import styled from 'styled-components';

export const ImageWrapper = styled.span`
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin-right: ${props => props.theme.spacing(0.5)};
  padding: ${props => props.theme.spacing(0.5)};
  background: ${props => props.theme.palette.grey[100]};
  border-radius: 4px;

  img {
    max-width: 60px;
    max-height: 40px;
    width: auto;
    height: auto;
    min-width: 40px;
    object-fit: cover;
    cursor: pointer;
    border-radius: 2px;
  }

  .cancel-icon {
    position: absolute;
    top: -4px;
    right: -4px;
    font-size: 16px;
  }

  .pdf-icon {
    font-size: 32px;
    cursor: pointer;
  }
`;

export default ImageWrapper;
