import { useState } from 'react';

import { Warning as WarningIcon } from '@mui/icons-material';
import {
  Chip,
  Tooltip,
  ClickAwayListener,
  Paper,
  Typography,
  Box,
  Divider,
} from '@mui/material';
import PropTypes from 'prop-types';

const CreditBreachTag = ({ breachedCustomers = [] }) => {
  const [tooltipOpen, setTooltipOpen] = useState(false);

  if (!breachedCustomers || breachedCustomers.length === 0) {
    return null;
  }

  const handleTooltipToggle = () => {
    setTooltipOpen(!tooltipOpen);
  };

  const handleTooltipClose = () => {
    setTooltipOpen(false);
  };

  const renderTooltipContent = () => {
    return (
      <Paper 
        elevation={0} 
        sx={{ 
          p: 1, 
          maxWidth: 300,
          bgcolor: 'rgba(255, 255, 255, 0.95)',
        }}
      >
        <Typography variant='subtitle2' sx={{ mb: 1, fontWeight: 'bold' }}>
          {breachedCustomers.length === 1 ? 'Credit Breach' : `Credit Breaches (${breachedCustomers.length})`}
        </Typography>
        <Divider sx={{ mb: 1 }} />
        {breachedCustomers.map((breach, index) => (
          <Box key={index} sx={{ mb: index < breachedCustomers.length - 1 ? 1 : 0 }}>
            <Typography variant='body2' sx={{ fontSize: '0.75rem' }}>
              • Credit breach against {breach.customerName} for Mandi No {breach.mandiNumber}
            </Typography>
          </Box>
        ))}
      </Paper>
    );
  };

  const getChipLabel = () => {
    if (breachedCustomers.length === 1) {
      return 'Credit Breach';
    }
    return `${breachedCustomers.length} Credit Breaches`;
  };

  return (
    <ClickAwayListener onClickAway={handleTooltipClose}>
      <Box sx={{ display: 'inline-block' }}>
        <Tooltip
          title={renderTooltipContent()}
          open={tooltipOpen}
          disableHoverListener
          disableFocusListener
          disableTouchListener
          placement='top'
          componentsProps={{
            tooltip: {
              sx: {
                bgcolor: 'rgba(97, 97, 97, 0.95)',
                maxWidth: 350,
                '& .MuiTooltip-arrow': {
                  color: 'rgba(97, 97, 97, 0.95)',
                },
              },
            },
          }}
        >
          <Chip
            icon={<WarningIcon sx={{ fontSize: '14px' }} />}
            label={getChipLabel()}
            size='small'
            onClick={handleTooltipToggle}
            sx={{
              bgcolor: '#ffebee',
              color: '#c62828',
              border: '1px solid #ef5350',
              fontSize: '0.7rem',
              height: '20px',
              cursor: 'pointer',
              '& .MuiChip-icon': {
                color: '#c62828',
              },
              '&:hover': {
                bgcolor: '#ffcdd2',
              },
            }}
          />
        </Tooltip>
      </Box>
    </ClickAwayListener>
  );
};

CreditBreachTag.propTypes = {
  breachedCustomers: PropTypes.arrayOf(
    PropTypes.shape({
      customerName: PropTypes.string.isRequired,
      mandiNumber: PropTypes.string.isRequired,
      customerId: PropTypes.number,
    })
  ),
};

export default CreditBreachTag;
