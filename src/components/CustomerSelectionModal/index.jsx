import React, { useState } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  List,
  ListItem,
  ListItemText,
  Typography,
  Box,
  Checkbox,
  FormControlLabel,
  Divider,
  IconButton,
} from '@mui/material';
import { Visibility as VisibilityIcon } from '@mui/icons-material';

const CustomerSelectionModal = ({ 
  open, 
  onClose, 
  customers, 
  onSelectCustomer, 
  onSelectMultiple 
}) => {
  const [selectedCustomers, setSelectedCustomers] = useState([]);
  const [selectAll, setSelectAll] = useState(false);

  // Handle individual checkbox change
  const handleCustomerToggle = (mandiNumber) => {
    setSelectedCustomers(prev => {
      const newSelected = prev.includes(mandiNumber)
        ? prev.filter(c => c !== mandiNumber)
        : [...prev, mandiNumber];
      
      // Update select all state
      setSelectAll(newSelected.length === customers.length);
      
      return newSelected;
    });
  };

  // Handle select all checkbox
  const handleSelectAllToggle = () => {
    if (selectAll) {
      setSelectedCustomers([]);
      setSelectAll(false);
    } else {
      setSelectedCustomers(customers.map(c => c.customerCode)); // customerCode contains mandi number
      setSelectAll(true);
    }
  };

  // Handle download selected
  const handleDownloadSelected = () => {
    if (selectedCustomers.length > 0) {
      onSelectMultiple(selectedCustomers);
      // Reset state
      setSelectedCustomers([]);
      setSelectAll(false);
    }
  };

  // Handle single mandi selection (individual preview)
  const handleSingleSelect = (mandiNumber) => {
    onSelectCustomer(mandiNumber);
    // Reset state
    setSelectedCustomers([]);
    setSelectAll(false);
  };

  // Handle modal close
  const handleClose = () => {
    setSelectedCustomers([]);
    setSelectAll(false);
    onClose();
  };

  return (
    <Dialog open={open} onClose={handleClose} maxWidth="sm" fullWidth>
      <DialogTitle>
        <Typography variant="h6" component="div">
          Select Mandi Numbers for Customer Slip
        </Typography>
      </DialogTitle>
      <DialogContent>
        <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
          Choose mandi numbers to generate customer slips:
        </Typography>
        
        {/* Select All Option */}
        <Box sx={{ mb: 2 }}>
          <FormControlLabel
            control={
              <Checkbox
                checked={selectAll}
                onChange={handleSelectAllToggle}
                color="primary"
              />
            }
            label={
              <Typography variant="subtitle2" fontWeight="medium">
                Select All ({customers.length} mandi numbers)
              </Typography>
            }
          />
        </Box>
        
        <Divider sx={{ mb: 2 }} />
        
        {/* Individual Mandi Number Options */}
        <List>
          {customers.map((customer, index) => (
            <ListItem key={index} disablePadding sx={{ mb: 1 }}>
              <Box
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                  width: '100%',
                  border: '1px solid #e0e0e0',
                  borderRadius: 1,
                  p: 1,
                }}
              >
                {/* Checkbox */}
                <Checkbox
                  checked={selectedCustomers.includes(customer.customerCode)}
                  onChange={() => handleCustomerToggle(customer.customerCode)}
                  color="primary"
                />
                
                {/* Mandi Info */}
                <ListItemText
                  primary={
                    <Typography variant="subtitle1" fontWeight="medium">
                      Mandi No: {customer.customerCode}
                    </Typography>
                  }
                  secondary={
                    <Box>
                      <Typography variant="body2" color="text.secondary">
                        Lots: {customer.lotCount}
                      </Typography>
                    </Box>
                  }
                  sx={{ flex: 1, ml: 1 }}
                />
                
                {/* Individual Preview Eye Icon */}
                <IconButton
                  size="small"
                  onClick={() => handleSingleSelect(customer.customerCode)}
                  title="Preview PDF"
                  sx={{ ml: 2 }}
                >
                  <VisibilityIcon fontSize="small" color="primary" />
                </IconButton>
              </Box>
            </ListItem>
          ))}
        </List>
      </DialogContent>
      <DialogActions sx={{ justifyContent: 'space-between', px: 3, pb: 2 }}>
        <Button onClick={handleClose} color="secondary">
          Cancel
        </Button>
        
        <Box>
          <Typography variant="body2" color="text.secondary" sx={{ mb: 1, textAlign: 'right' }}>
            {selectedCustomers.length} selected
          </Typography>
          <Button
            variant="contained"
            color="primary"
            onClick={handleDownloadSelected}
            disabled={selectedCustomers.length === 0}
            sx={{ minWidth: 140 }}
          >
            Print Selected ({selectedCustomers.length})
          </Button>
        </Box>
      </DialogActions>
    </Dialog>
  );
};

export default CustomerSelectionModal;
