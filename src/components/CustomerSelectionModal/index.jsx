import React, { useState } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  List,
  ListItem,
  Typography,
  Box,
  Checkbox,
  FormControlLabel,
  IconButton,
} from '@mui/material';
import { Visibility as VisibilityIcon } from '@mui/icons-material';

const CustomerSelectionModal = ({
  open,
  onClose,
  customers,
  onSelectCustomer,
  onSelectMultiple,
}) => {
  const [selectedCustomers, setSelectedCustomers] = useState([]);
  const [selectAll, setSelectAll] = useState(false);

  // Handle individual checkbox change
  const handleCustomerToggle = mandiNumber => {
    setSelectedCustomers(prev => {
      const newSelected = prev.includes(mandiNumber)
        ? prev.filter(c => c !== mandiNumber)
        : [...prev, mandiNumber];

      // Update select all state
      setSelectAll(newSelected.length === customers.length);

      return newSelected;
    });
  };

  // <PERSON>le select all checkbox
  const handleSelectAllToggle = () => {
    if (selectAll) {
      setSelectedCustomers([]);
      setSelectAll(false);
    } else {
      setSelectedCustomers(customers.map(c => c.customerCode)); // customerCode contains mandi number
      setSelectAll(true);
    }
  };

  // Handle download selected
  const handleDownloadSelected = () => {
    if (selectedCustomers.length > 0) {
      onSelectMultiple(selectedCustomers);
      // Reset state
      setSelectedCustomers([]);
      setSelectAll(false);
    }
  };

  // Handle single mandi selection (individual preview)
  const handleSingleSelect = mandiNumber => {
    onSelectCustomer(mandiNumber);
    // Reset state
    setSelectedCustomers([]);
    setSelectAll(false);
  };

  // Handle modal close
  const handleClose = () => {
    setSelectedCustomers([]);
    setSelectAll(false);
    onClose();
  };

  return (
    <Dialog open={open} onClose={handleClose} maxWidth='md' fullWidth>
      <DialogTitle sx={{ pb: 1 }}>
        <Typography
          variant='h5'
          component='div'
          fontWeight='600'
          color='primary.main'
        >
          Select Mandi Numbers for Customer Slip
        </Typography>
        <Typography variant='body2' color='text.secondary' sx={{ mt: 1 }}>
          Choose mandi numbers to generate customer slips
        </Typography>
      </DialogTitle>
      <DialogContent sx={{ pt: 2 }}>
        {/* Select All Option */}
        <Box
          sx={{
            mb: 3,
            p: 2,
            backgroundColor: 'grey.50',
            borderRadius: 2,
            border: '1px solid',
            borderColor: 'grey.200',
          }}
        >
          <FormControlLabel
            control={
              <Checkbox
                checked={selectAll}
                onChange={handleSelectAllToggle}
                color='primary'
                size='medium'
              />
            }
            label={
              <Typography
                variant='subtitle1'
                fontWeight='600'
                color='text.primary'
              >
                Select All ({customers.length} Mandi Numbers)
              </Typography>
            }
          />
        </Box>

        {/* Individual Mandi Number Options */}
        <List sx={{ p: 0 }}>
          {customers.map((customer, index) => (
            <ListItem key={index} disablePadding sx={{ mb: 2 }}>
              <Box
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                  width: '100%',
                  border: '1px solid',
                  borderColor: 'grey.300',
                  borderRadius: 2,
                  p: 2,
                  backgroundColor: 'white',
                  boxShadow: '0 1px 3px rgba(0,0,0,0.1)',
                  transition: 'all 0.2s ease-in-out',
                  '&:hover': {
                    borderColor: 'primary.main',
                    boxShadow: '0 2px 8px rgba(0,0,0,0.15)',
                  },
                }}
              >
                {/* Checkbox */}
                <Checkbox
                  checked={selectedCustomers.includes(customer.customerCode)}
                  onChange={() => handleCustomerToggle(customer.customerCode)}
                  color='primary'
                  size='medium'
                  sx={{ mr: 2 }}
                />

                {/* Mandi Info */}
                <Box sx={{ flex: 1 }}>
                  <Typography
                    variant='h6'
                    fontWeight='600'
                    color='text.primary'
                    sx={{ mb: 0.5 }}
                  >
                    Mandi No: {customer.customerCode}
                  </Typography>
                  <Typography variant='body2' color='text.secondary'>
                    Lots: {customer.lotCount}
                  </Typography>
                </Box>

                {/* Individual Preview Eye Icon */}
                <IconButton
                  size='medium'
                  onClick={() => handleSingleSelect(customer.customerCode)}
                  title='Preview Customer Slip'
                  sx={{
                    ml: 2,
                    backgroundColor: 'primary.main',
                    color: 'white',
                    '&:hover': {
                      backgroundColor: 'primary.dark',
                    },
                  }}
                >
                  <VisibilityIcon fontSize='small' />
                </IconButton>
              </Box>
            </ListItem>
          ))}
        </List>
      </DialogContent>
      <DialogActions
        sx={{
          justifyContent: 'space-between',
          px: 3,
          py: 3,
          backgroundColor: 'grey.50',
        }}
      >
        <Button
          onClick={handleClose}
          color='secondary'
          variant='outlined'
          size='large'
          sx={{ minWidth: 100 }}
        >
          Cancel
        </Button>

        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          <Typography variant='body1' color='text.secondary' fontWeight='500'>
            {selectedCustomers.length} Selected
          </Typography>
          <Button
            variant='contained'
            color='primary'
            onClick={handleDownloadSelected}
            disabled={selectedCustomers.length === 0}
            size='large'
            sx={{
              minWidth: 180,
              fontWeight: '600',
              textTransform: 'none',
            }}
          >
            Print Selected ({selectedCustomers.length})
          </Button>
        </Box>
      </DialogActions>
    </Dialog>
  );
};

export default CustomerSelectionModal;
