import styled from 'styled-components';
import { Box } from '@mui/material';

export const StatsContainer = styled(Box)`
  display: flex;
  flex-direction: column;
  gap: 8px;
`;

export const StatItem = styled(Box)`
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding: 16px 12px;
  border-radius: 12px;
  background: rgba(153, 51, 51, 0.02); /* Very light primary color background */
  transition: all 0.2s ease-in-out;
  min-height: 80px;
  justify-content: center;

  &:hover {
    background: rgba(153, 51, 51, 0.05);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(153, 51, 51, 0.1);
  }

  @media (min-width: 768px) {
    padding: 20px 16px;
    min-height: 100px;
  }
`;

export const StatValue = styled.div`
  font-size: 1.75rem;
  font-weight: 700;
  color: #993333; /* Primary color */
  line-height: 1.1;
  margin-bottom: 4px;

  @media (min-width: 768px) {
    font-size: 2rem;
  }

  @media (min-width: 1024px) {
    font-size: 2.25rem;
  }
`;

export const StatLabel = styled.div`
  font-size: 0.75rem;
  font-weight: 600;
  color: #4A5568; /* Dark gray */
  text-transform: uppercase;
  letter-spacing: 0.5px;
  line-height: 1.2;
  text-align: center;

  @media (min-width: 768px) {
    font-size: 0.8rem;
  }
`;

export const IconWrapper = styled(Box)`
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
  border-radius: 8px;
  background: rgba(153, 51, 51, 0.1); /* Light primary background */
  color: #993333; /* Primary color */

  @media (min-width: 768px) {
    width: 32px;
    height: 32px;
    border-radius: 10px;
  }
`;
