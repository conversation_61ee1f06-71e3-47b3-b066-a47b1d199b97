import React, { useEffect, useState } from 'react';

import { Card, CardContent, Typography, Box, Grid } from '@mui/material';
import {
  Inventory2Outlined,
  PeopleOutlined
} from '@mui/icons-material';

import { useSiteValue } from 'App/SiteContext';
import { getSummaryDetail } from 'Services/summary';

import { StatItem, StatValue, StatLabel, IconWrapper } from './styles';

const StatsSummaryTile = () => {
  const [data, setData] = useState({});
  const [loading, setLoading] = useState(true);
  const { mandiId, auctionDate } = useSiteValue();

  useEffect(() => {
    if (mandiId) {
      setLoading(true);
      getSummaryDetail({ mandi_id: mandiId, auction_date: auctionDate })
        .then(({ responseData = {} }) => {
          setData(responseData);
        })
        .catch((error) => {
          console.error('Error fetching stats:', error);
        })
        .finally(() => {
          setLoading(false);
        });
    }
  }, [mandiId, auctionDate]);

  const {
    total_units_sold = 0,
    total_full_boxes = 0,
    total_half_boxes = 0,
    total_boxes = 0,
    total_rtf_units = 0,
    total_farmers = 0,
    total_loaders = 0,
  } = data || {};

  if (loading) {
    return (
      <Card
        sx={{
          borderRadius: 3,
          boxShadow: '0 2px 12px rgba(0,0,0,0.08)',
          border: '1px solid #E2E8F0',
          minHeight: 200,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
        }}
      >
        <Typography variant="body2" color="text.secondary">
          Loading stats...
        </Typography>
      </Card>
    );
  }

  return (
    <Card
      sx={{
        borderRadius: 3,
        boxShadow: '0 2px 12px rgba(0,0,0,0.08)',
        border: '1px solid #E2E8F0',
        transition: 'all 0.3s ease-in-out',
        '&:hover': {
          transform: 'translateY(-2px)',
          boxShadow: '0 8px 25px rgba(0,0,0,0.12)',
        },
      }}
    >
      <CardContent sx={{ padding: { xs: 3, md: 4 } }}>
        {/* Header */}
        <Box sx={{ mb: 4, textAlign: 'center' }}>
          <Typography
            variant="h5"
            sx={{
              fontWeight: 600,
              color: 'primary.main',
              mb: 0.5
            }}
          >
            Today's Summary
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Auction Statistics
          </Typography>
        </Box>

        {/* Main Content - Three sections layout */}
        <Grid container spacing={{ xs: 3, md: 4 }} alignItems="flex-start">

          {/* Total Boxes Section */}
          <Grid item xs={12} md={4}>
            <Box>
              <Typography
                variant="h6"
                sx={{
                  fontWeight: 600,
                  color: 'text.primary',
                  mb: 3,
                  display: 'flex',
                  alignItems: 'center',
                  gap: 1.5,
                  justifyContent: { xs: 'center', md: 'flex-start' }
                }}
              >
                <IconWrapper sx={{ width: 32, height: 32 }}>
                  <Inventory2Outlined sx={{ fontSize: 20 }} />
                </IconWrapper>
                Total Boxes
              </Typography>

              <Grid container spacing={{ xs: 2, md: 2 }}>
                <Grid item xs={6} md={6}>
                  <StatItem>
                    <StatValue>{total_half_boxes}</StatValue>
                    <StatLabel>Half Boxes</StatLabel>
                  </StatItem>
                </Grid>
                <Grid item xs={6} md={6}>
                  <StatItem>
                    <StatValue>{total_full_boxes}</StatValue>
                    <StatLabel>Full Boxes</StatLabel>
                  </StatItem>
                </Grid>
                <Grid item xs={12}>
                  <StatItem sx={{ background: 'rgba(153, 51, 51, 0.08)' }}>
                    <StatValue>{total_boxes}</StatValue>
                    <StatLabel>Total Boxes</StatLabel>
                  </StatItem>
                </Grid>
              </Grid>
            </Box>
          </Grid>

          {/* Sold Boxes & RTF Boxes Section */}
          <Grid item xs={12} md={4}>
            <Box>
              <Typography
                variant="h6"
                sx={{
                  fontWeight: 600,
                  color: 'text.primary',
                  mb: 3,
                  display: 'flex',
                  alignItems: 'center',
                  gap: 1.5,
                  justifyContent: { xs: 'center', md: 'flex-start' }
                }}
              >
                <IconWrapper sx={{ width: 32, height: 32 }}>
                  <Inventory2Outlined sx={{ fontSize: 20 }} />
                </IconWrapper>
                Sold & RTF
              </Typography>

              <Grid container spacing={{ xs: 2, md: 2 }}>
                <Grid item xs={6} md={12}>
                  <StatItem>
                    <StatValue>{total_units_sold}</StatValue>
                    <StatLabel>Sold Boxes</StatLabel>
                  </StatItem>
                </Grid>
                <Grid item xs={6} md={12}>
                  <StatItem>
                    <StatValue>{total_rtf_units}</StatValue>
                    <StatLabel>RTF Boxes</StatLabel>
                  </StatItem>
                </Grid>
              </Grid>
            </Box>
          </Grid>

          {/* Farmers & Loaders Section */}
          <Grid item xs={12} md={4}>
            <Box>
              <Typography
                variant="h6"
                sx={{
                  fontWeight: 600,
                  color: 'text.primary',
                  mb: 3,
                  display: 'flex',
                  alignItems: 'center',
                  gap: 1.5,
                  justifyContent: { xs: 'center', md: 'flex-start' }
                }}
              >
                <IconWrapper sx={{ width: 32, height: 32 }}>
                  <PeopleOutlined sx={{ fontSize: 20 }} />
                </IconWrapper>
                People
              </Typography>

              <Grid container spacing={{ xs: 2, md: 2 }}>
                <Grid item xs={6} md={12}>
                  <StatItem>
                    <StatValue>{total_farmers}</StatValue>
                    <StatLabel>Farmers</StatLabel>
                  </StatItem>
                </Grid>
                <Grid item xs={6} md={12}>
                  <StatItem>
                    <StatValue>{total_loaders}</StatValue>
                    <StatLabel>Loaders</StatLabel>
                  </StatItem>
                </Grid>
              </Grid>
            </Box>
          </Grid>
        </Grid>
      </CardContent>
    </Card>
  );
};

export default StatsSummaryTile;
